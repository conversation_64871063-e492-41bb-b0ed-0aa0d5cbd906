#!/bin/bash

# iCalDZ Activation Code Generator - Unix/Linux/macOS Shell Script
# This script provides an easy interface for generating activation codes

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo -e "${CYAN}"
echo "========================================"
echo "   iCalDZ Activation Code Generator"
echo "========================================"
echo -e "${NC}"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}ERROR: Node.js is not installed or not in PATH${NC}"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if the generator script exists
if [ ! -f "$SCRIPT_DIR/generateActivationCodes.js" ]; then
    echo -e "${RED}ERROR: generateActivationCodes.js not found${NC}"
    echo "Please ensure the script is in the same directory as this shell script"
    exit 1
fi

show_menu() {
    echo
    echo "Select an option:"
    echo
    echo "1. Generate 10 lifetime codes"
    echo "2. Generate 50 lifetime codes"
    echo "3. Generate 100 lifetime codes"
    echo "4. Generate 7-day trial codes"
    echo "5. Generate 30-day trial codes"
    echo "6. Custom generation"
    echo "7. View help"
    echo "8. Exit"
    echo
}

generate_lifetime_codes() {
    local count=$1
    echo
    echo -e "${YELLOW}Generating $count lifetime activation codes...${NC}"
    node "$SCRIPT_DIR/generateActivationCodes.js" --count $count --type lifetime
}

generate_trial_codes() {
    local type=$1
    local default_count=$2
    
    echo
    read -p "Enter number of $type trial codes to generate (default $default_count): " trial_count
    trial_count=${trial_count:-$default_count}
    
    echo -e "${YELLOW}Generating $trial_count $type trial codes...${NC}"
    node "$SCRIPT_DIR/generateActivationCodes.js" --count $trial_count --type "trial-$type"
}

custom_generation() {
    echo
    echo -e "${PURPLE}Custom Code Generation${NC}"
    echo "====================="
    echo
    
    read -p "Enter number of codes (1-1000): " custom_count
    
    echo
    echo "Code types:"
    echo "1. Lifetime (permanent)"
    echo "2. 7-day trial"
    echo "3. 30-day trial"
    echo
    
    read -p "Select code type (1-3): " type_choice
    
    case $type_choice in
        1) custom_type="lifetime" ;;
        2) custom_type="trial-7" ;;
        3) custom_type="trial-30" ;;
        *)
            echo -e "${RED}Invalid type selection.${NC}"
            custom_generation
            return
            ;;
    esac
    
    echo
    read -p "Enter batch name (optional): " batch_name
    read -p "Enter output filename (optional): " output_file
    
    echo
    echo -e "${YELLOW}Generating $custom_count $custom_type codes...${NC}"
    
    cmd_args="--count $custom_count --type $custom_type"
    
    if [ ! -z "$batch_name" ]; then
        cmd_args="$cmd_args --batch-name \"$batch_name\""
    fi
    
    if [ ! -z "$output_file" ]; then
        cmd_args="$cmd_args --output \"$output_file\""
    fi
    
    eval "node \"$SCRIPT_DIR/generateActivationCodes.js\" $cmd_args"
}

show_help() {
    echo
    node "$SCRIPT_DIR/generateActivationCodes.js" --help
}

continue_prompt() {
    echo
    echo "========================================"
    read -p "Do you want to generate more codes? (y/n): " continue_choice
    
    case $continue_choice in
        [Yy]|[Yy][Ee][Ss])
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Main loop
while true; do
    show_menu
    read -p "Enter your choice (1-8): " choice
    
    case $choice in
        1)
            generate_lifetime_codes 10
            ;;
        2)
            generate_lifetime_codes 50
            ;;
        3)
            generate_lifetime_codes 100
            ;;
        4)
            generate_trial_codes "7" 10
            ;;
        5)
            generate_trial_codes "30" 10
            ;;
        6)
            custom_generation
            ;;
        7)
            show_help
            ;;
        8)
            echo
            echo -e "${GREEN}Thank you for using iCalDZ Code Generator!${NC}"
            echo
            exit 0
            ;;
        *)
            echo -e "${RED}Invalid choice. Please try again.${NC}"
            continue
            ;;
    esac
    
    if ! continue_prompt; then
        echo
        echo -e "${GREEN}Thank you for using iCalDZ Code Generator!${NC}"
        echo
        break
    fi
done
