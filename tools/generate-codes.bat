@echo off
REM iCalDZ Activation Code Generator - Windows Batch File
REM This script provides an easy interface for generating activation codes

title iCalDZ Code Generator

echo.
echo ========================================
echo    iCalDZ Activation Code Generator
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if the generator script exists
if not exist "%~dp0generateActivationCodes.js" (
    echo ERROR: generateActivationCodes.js not found
    echo Please ensure the script is in the same directory as this batch file
    pause
    exit /b 1
)

:MENU
echo.
echo Select an option:
echo.
echo 1. Generate 10 lifetime codes
echo 2. Generate 50 lifetime codes
echo 3. Generate 100 lifetime codes
echo 4. Generate 3-day trial codes
echo 5. Generate 7-day trial codes
echo 6. Generate 30-day trial codes
echo 7. Custom generation
echo 8. View help
echo 9. Exit
echo.
set /p choice="Enter your choice (1-9): "

if "%choice%"=="1" goto LIFETIME_10
if "%choice%"=="2" goto LIFETIME_50
if "%choice%"=="3" goto LIFETIME_100
if "%choice%"=="4" goto TRIAL_3
if "%choice%"=="5" goto TRIAL_7
if "%choice%"=="6" goto TRIAL_30
if "%choice%"=="7" goto CUSTOM
if "%choice%"=="8" goto HELP
if "%choice%"=="9" goto EXIT

echo Invalid choice. Please try again.
goto MENU

:LIFETIME_10
echo.
echo Generating 10 lifetime activation codes...
node "%~dp0generateActivationCodes.js" --count 10 --type lifetime
goto CONTINUE

:LIFETIME_50
echo.
echo Generating 50 lifetime activation codes...
node "%~dp0generateActivationCodes.js" --count 50 --type lifetime
goto CONTINUE

:LIFETIME_100
echo.
echo Generating 100 lifetime activation codes...
node "%~dp0generateActivationCodes.js" --count 100 --type lifetime
goto CONTINUE

:TRIAL_3
echo.
set /p trial_count="Enter number of 3-day trial codes to generate (default 10): "
if "%trial_count%"=="" set trial_count=10
echo Generating %trial_count% 3-day trial codes...
node "%~dp0generateActivationCodes.js" --count %trial_count% --type trial-3
goto CONTINUE

:TRIAL_7
echo.
set /p trial_count="Enter number of 7-day trial codes to generate (default 10): "
if "%trial_count%"=="" set trial_count=10
echo Generating %trial_count% 7-day trial codes...
node "%~dp0generateActivationCodes.js" --count %trial_count% --type trial-7
goto CONTINUE

:TRIAL_30
echo.
set /p trial_count="Enter number of 30-day trial codes to generate (default 10): "
if "%trial_count%"=="" set trial_count=10
echo Generating %trial_count% 30-day trial codes...
node "%~dp0generateActivationCodes.js" --count %trial_count% --type trial-30
goto CONTINUE

:CUSTOM
echo.
echo Custom Code Generation
echo =====================
echo.
set /p custom_count="Enter number of codes (1-1000): "
echo.
echo Code types:
echo 1. Lifetime (permanent)
echo 2. 3-day trial
echo 3. 7-day trial
echo 4. 30-day trial
echo.
set /p type_choice="Select code type (1-4): "

if "%type_choice%"=="1" set custom_type=lifetime
if "%type_choice%"=="2" set custom_type=trial-3
if "%type_choice%"=="3" set custom_type=trial-7
if "%type_choice%"=="4" set custom_type=trial-30

if "%custom_type%"=="" (
    echo Invalid type selection.
    goto CUSTOM
)

echo.
set /p batch_name="Enter batch name (optional): "
set /p output_file="Enter output filename (optional): "

echo.
echo Generating %custom_count% %custom_type% codes...

set cmd_args=--count %custom_count% --type %custom_type%
if not "%batch_name%"=="" set cmd_args=%cmd_args% --batch-name "%batch_name%"
if not "%output_file%"=="" set cmd_args=%cmd_args% --output "%output_file%"

node "%~dp0generateActivationCodes.js" %cmd_args%
goto CONTINUE

:HELP
echo.
node "%~dp0generateActivationCodes.js" --help
goto CONTINUE

:CONTINUE
echo.
echo ========================================
set /p continue_choice="Do you want to generate more codes? (y/n): "
if /i "%continue_choice%"=="y" goto MENU
if /i "%continue_choice%"=="yes" goto MENU

:EXIT
echo.
echo Thank you for using iCalDZ Code Generator!
echo.
pause
exit /b 0
