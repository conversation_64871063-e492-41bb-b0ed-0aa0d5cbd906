
# 🔐 Activation Code Generator with Supabase

This script generates unique activation codes like `ICAL-2025-XXXX-XXXX-XXXX` and inserts them into your Supabase `activations` table.

---

## 📦 Requirements

```bash
npm install @supabase/supabase-js
```

---

## 🧠 Setup

Replace with your own Supabase credentials:

```js
const SUPABASE_URL = 'https://YOUR_PROJECT.supabase.co';
const SUPABASE_KEY = 'your-service-role-key';
```

Use the **Service Role Key** (never expose in frontend apps).

---

## 🧾 Example Supabase Table: `activations`

| Column Name      | Type       |
|------------------|------------|
| activation_code  | text       |
| status           | text       |
| device_id        | text       |
| activated_at     | timestamp  |

---

## 🧰 Node.js Script Example

```js
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://YOUR_PROJECT.supabase.co';
const SUPABASE_KEY = 'your-service-role-key';
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

function generateCode() {
  const part = () => Math.random().toString(36).substring(2, 6).toUpperCase();
  return `ICAL-2025-${part()}-${part()}-${part()}`;
}

async function saveCodes(count = 10) {
  const codes = Array.from({ length: count }, () => ({
    activation_code: generateCode(),
    status: 'unused',
  }));

  const { data, error } = await supabase.from('activations').insert(codes);
  if (error) {
    console.error('❌ Error inserting codes:', error);
  } else {
    console.log(`✅ Inserted ${data.length} activation codes.`);
  }
}

saveCodes(20);
```

---

## ✅ Result

You will see 20 new unique codes inserted into your Supabase table like:

```
ICAL-2025-ABCD-EFGH-IJKL
ICAL-2025-X7YY-123A-B90Z
...
```

---

## 🔐 Security Tip

- Keep the `SUPABASE_KEY` secret
- Run this script **only on your trusted machine/server**
