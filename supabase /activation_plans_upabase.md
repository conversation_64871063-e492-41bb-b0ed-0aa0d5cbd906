
## ✅ Activation System Implementation Plan (Using Supabase)

---

### 🎯 Goal:
Create a secure activation system for your Electron desktop app, where:
- The user enters a one-time activation code
- The app verifies the code online via Supabase
- Once activated, the app can be used offline on the same machine only

---

## 🧱 Step 1: Create Supabase Database

Table name: `activations`

| Field            | Type       | Description                                     |
|------------------|------------|-------------------------------------------------|
| id               | UUID       | Auto-generated ID                              |
| activation_code  | text       | One-time code (e.g., ICAL-2025-XXXX...)        |
| device_id        | text       | Machine ID of the activated device             |
| status           | text       | unused / activated / blocked                   |
| activated_at     | timestamp  | Date and time of activation                    |
| user_name        | text       | (Optional) Name of the client                  |

---

## 🖥️ Step 2: Activation Interface in the App

On first launch:
- The app displays an interface requesting the activation code
- After input, the app fetches the device machine ID:

```bash
npm install node-machine-id
```

```js
import { machineIdSync } from 'node-machine-id';
const deviceId = machineIdSync();
```

---

## 🌐 Step 3: Online Verification with Supabase

- The app sends the activation code and deviceId to Supabase
- Supabase checks:
  - Is the code valid?
  - Is the status "unused"?

**If valid:**
- The code is bound to the deviceId
- Status is updated to "activated"
- License is saved locally (encrypted)

📦 Supabase client:
```bash
npm install @supabase/supabase-js
```

```js
import { createClient } from '@supabase/supabase-js';
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

const { data, error } = await supabase
  .from('activations')
  .select('*')
  .eq('activation_code', inputCode)
  .single();

if (data && data.status === 'unused') {
  await supabase
    .from('activations')
    .update({ device_id, status: 'activated', activated_at: new Date().toISOString() })
    .eq('activation_code', inputCode);
  // Proceed to local save
}
```

---

## 🔐 Step 4: Save Encrypted Activation Locally

```bash
npm install crypto-js electron-store
```

```js
import CryptoJS from 'crypto-js';
import Store from 'electron-store';

const store = new Store();
const data = {
  activation_code,
  device_id,
  activated: true,
  activated_at: new Date().toISOString()
};

const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), 'your-secret-key').toString();
store.set('license', encrypted);
```

---

## 📴 Step 5: Offline Verification on Launch

On every startup:
- Read and decrypt local activation file
- Validate:
  - Code is present
  - Device ID matches current machine
  - Activation is true

✅ If valid → Launch app
❌ If invalid → Prompt user to activate again

---

## 🔁 What if user reinstalls Windows?

- If machine ID stays the same → activation still valid
- If changed:
  - User must contact you
  - You update or reset the device_id in Supabase or give a new code

---

## 🛡️ Security Features:

| Threat                         | Recommended Solution                         |
|-------------------------------|----------------------------------------------|
| Code reused on multiple devices| Bind code to unique machineId               |
| Manual license file copying   | Encrypt using AES                           |
| Code reused without permission| Supabase prevents reuse once status updated |
| Reinstallation bypass         | Tie activation to machine, not installation |

---

## ✅ Final Checklist:

| Task                            | Status | Notes                                      |
|---------------------------------|--------|---------------------------------------------|
| Create table in Supabase        | ⬜     | Name it `activations`                      |
| Build React UI for activation   | ⬜     | Show on first launch                       |
| Supabase verification logic     | ⬜     | Use `@supabase/supabase-js`                |
| Get device ID                   | ⬜     | Use `node-machine-id`                      |
| Store activation locally        | ⬜     | Use `electron-store + crypto-js`           |
| Check activation offline        | ⬜     | Validate license file on each startup      |
