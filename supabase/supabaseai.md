# iCalDZ Activation System - Complete Documentation

## 🎯 Overview

The iCalDZ Activation System is a comprehensive, secure licensing solution that provides:
- **Device-specific activation** (one code per machine)
- **Multiple license types** (lifetime, 3-day, 7-day, 30-day trials)
- **Secure offline validation** with encrypted local storage
- **Multi-language support** (Arabic, English, French)
- **Modern activation interface** with beautiful UI
- **Bulk code management** with CSV import/export
- **Anti-tampering protection** and security monitoring

## 🏗️ System Architecture

### Database Schema (Supabase)
```sql
CREATE TABLE activations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  activation_code TEXT UNIQUE NOT NULL,
  device_id TEXT,
  status TEXT DEFAULT 'unused' CHECK (status IN ('unused', 'activated', 'blocked')),
  activated_at TIMESTAMP WITH TIME ZONE,
  user_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Code Format Standards
- **Lifetime**: `ICAL-YYYY-XXXX-XXXX-XXXX`
- **3-Day Trial**: `TRIAL3-YYYY-XXXX-XXXX-XXXX`
- **7-Day Trial**: `TRIAL7-YYYY-XXXX-XXXX-XXXX`
- **30-Day Trial**: `TRIAL30-YYYY-XXXX-XXXX-XXXX`

Where:
- `YYYY` = Current year
- `XXXX` = 4-character alphanumeric segments

## 🔧 Installation & Setup

### 1. Dependencies
```bash
npm install @supabase/supabase-js node-machine-id electron-store crypto-js
```

### 2. Supabase Configuration
- **Project**: iCalDZ Activation keys
- **URL**: `https://meaorrtisoruuoupldwq.supabase.co`
- **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### 3. File Structure
```
src/
├── config/
│   └── supabase.js              # Supabase configuration
├── services/
│   ├── ActivationService.js     # Main activation logic
│   ├── SecurityService.js       # Electron security features
│   └── WebSecurityService.js    # Web security fallback
├── components/
│   ├── ActivationWrapper.jsx    # App integration wrapper
│   └── LicenseStatus.jsx        # License management UI
├── pages/
│   └── ActivationPage.jsx       # Modern activation interface
└── translations.js              # Multi-language support

tools/
├── generateActivationCodes.js        # Code generation script
├── validateActivationCode.js         # Code validation tool
├── importCodesFromCSV.js             # Bulk CSV import
├── exportCodesFromSupabase.js        # CSV export tool
├── generate-codes.bat                # Windows batch script
├── generate-codes.sh                 # Mac/Linux shell script
├── iCalDZ-Code-Generator.command     # Mac double-click app
├── iCalDZ Code Generator.app/        # Mac application bundle
├── sample-codes.csv                  # Example CSV format
└── README-Mac.md                     # Mac-specific documentation
```

## 🎮 Usage Guide

### Generating Activation Codes

#### Method 1: Command Line
```bash
cd tools

# Generate 10 lifetime codes
node generateActivationCodes.js --count 10 --type lifetime

# Generate 5 trial codes
node generateActivationCodes.js --count 5 --type trial-7

# Generate with batch name and save to file
node generateActivationCodes.js --count 50 --type lifetime --batch-name "January 2025" --output "jan-codes.txt"
```

#### Method 2: Interactive Scripts
```bash
# Windows
generate-codes.bat

# Mac/Linux
./generate-codes.sh

# Mac (Double-click applications)
# Double-click: iCalDZ-Code-Generator.command
# Double-click: iCalDZ Code Generator.app
```

#### Method 3: CSV Bulk Import
```bash
# Import codes from CSV file
node importCodesFromCSV.js sample-codes.csv

# Validate only (don't import)
node importCodesFromCSV.js codes.csv --validate-only

# Import in smaller batches
node importCodesFromCSV.js codes.csv --batch-size 50
```

### CSV Format for Bulk Import
```csv
activation_code,user_name,status
ICAL-2025-ABCD-EFGH-IJKL,John Doe,unused
TRIAL7-2025-XYZ1-ABC2-DEF3,Trial User,unused
TRIAL30-2025-TEST-CODE-HERE,Beta Tester,unused
```

**Required Columns:**
- `activation_code`: The activation code (required)
- `user_name`: User or batch name (optional)
- `status`: unused, activated, or blocked (default: unused)

### Validating Codes
```bash
# Validate a single code
node validateActivationCode.js ICAL-2025-ABCD-EFGH-IJKL

# Interactive validation
node validateActivationCode.js --interactive
```

## 🎨 User Interface

### Modern Activation Page Features
- **3-Step Process**: Welcome → Code Entry → Success
- **Multi-language Support**: Arabic (RTL), English, French
- **Responsive Design**: Works on all screen sizes
- **Real-time Validation**: Format checking and error display
- **Device Information**: Shows device ID and binding info
- **Beautiful Animations**: Smooth transitions and feedback

### Language Support
The system supports three languages with complete translations:
- **Arabic (ar)**: Right-to-left layout with Arabic text
- **English (en)**: Standard left-to-right layout
- **French (fr)**: French translations for all UI elements

## 🔒 Security Features

### Device Binding
- Each activation code can only be used on one device
- Device ID generated using hardware fingerprinting
- Prevents code sharing and unauthorized usage

### Encryption
- **Multi-layer AES encryption** for local storage
- **Hardware fingerprinting** for additional security
- **Session validation** with automatic expiry

### Anti-Tampering
- **Developer tools detection**
- **Debugging attempt monitoring**
- **Suspicious activity tracking**
- **Security violation logging**

### Offline Protection
- **Encrypted local validation** works without internet
- **Tamper-resistant storage** prevents modification
- **Automatic security checks** on app startup

## 📊 Management & Monitoring

### Supabase Dashboard
Access your Supabase project to:
- View all activation codes and their status
- Monitor activation attempts and usage
- Block or unblock specific codes
- Export usage reports

### Code Status Management
- **unused**: Available for activation
- **activated**: Successfully activated on a device
- **blocked**: Disabled and cannot be used

### Bulk Operations
```bash
# Generate 1000 lifetime codes for distribution
node generateActivationCodes.js --count 1000 --type lifetime --batch-name "Production Batch 1" --output "production-codes.txt"

# Import pre-generated codes from CSV
node importCodesFromCSV.js production-codes.csv --batch-size 100

# Validate codes before distribution
node validateActivationCode.js --interactive
```

## 🚀 Integration Guide

### App Integration
The activation system is automatically integrated into your app through the `ActivationWrapper` component:

```jsx
function App() {
  return (
    <LanguageProvider>
      <ActivationWrapper>
        <AppContent />
      </ActivationWrapper>
    </LanguageProvider>
  );
}
```

### Activation Flow
1. **App Startup**: Check for existing activation
2. **No Activation**: Show modern activation page
3. **Code Entry**: User enters activation code
4. **Validation**: Verify code with Supabase
5. **Device Binding**: Bind code to current device
6. **Local Storage**: Save encrypted activation data
7. **App Access**: Allow full app functionality

### Offline Operation
After initial activation, the app works completely offline:
- Local encrypted validation on startup
- No internet required for daily use
- Secure session management
- Automatic security checks

## 🛠️ Troubleshooting

### Common Issues

#### White Screen on Startup
- **Cause**: Module import issues in web environment or Supabase connection problems
- **Solution**:
  1. Use test mode by adding `?test=true` to URL: `http://localhost:3000/?test=true`
  2. The system automatically falls back to web-compatible modules
  3. Test mode bypasses Supabase and allows UI testing
  4. For production, ensure Supabase credentials are correct

#### Code Not Working
- **Check Format**: Ensure code matches expected pattern
- **Verify Status**: Use validation tool to check code status
- **Device Binding**: Code may already be used on another device

#### Import Errors
- **CSV Format**: Ensure proper column headers and data format
- **Duplicate Codes**: System prevents importing existing codes
- **Batch Size**: Reduce batch size if experiencing timeouts

### Debug Commands
```bash
# Check code status
node validateActivationCode.js YOUR-CODE-HERE

# Test CSV format
node importCodesFromCSV.js your-file.csv --validate-only

# Generate test codes
node generateActivationCodes.js --count 1 --type trial-3 --output "test.txt"
```

## 📈 Best Practices

### Code Distribution
1. **Generate in batches** with descriptive names
2. **Export to secure files** for distribution
3. **Track usage** through Supabase dashboard
4. **Monitor activation patterns** for security

### Security
1. **Regular monitoring** of activation attempts
2. **Block suspicious codes** immediately
3. **Use trial codes** for evaluation periods
4. **Implement usage analytics** for insights

### User Experience
1. **Provide clear instructions** for activation
2. **Offer multiple language options**
3. **Include support contact** information
4. **Test activation flow** regularly

## 🔗 API Reference

### ActivationService Methods
```javascript
// Initialize the service
await activationService.initialize();

// Activate with code
const result = await activationService.activate(code, userName);

// Check activation status
const status = activationService.getActivationStatus();

// Clear activation (for testing)
activationService.clearLocalActivation();
```

### Supabase Operations
```javascript
// Query activations table
const { data, error } = await supabase
  .from('activations')
  .select('*')
  .eq('status', 'unused');

// Update code status
const { error } = await supabase
  .from('activations')
  .update({ status: 'blocked' })
  .eq('activation_code', 'ICAL-2025-XXXX-XXXX-XXXX');
```

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Documentation**: This file and inline code comments
- **Tools**: Use the validation and generation tools for testing

---

**Developed by iCode DZ** - Complete activation system for iCalDZ accounting software.
