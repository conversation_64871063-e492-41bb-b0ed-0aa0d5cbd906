Project URL : https://meaorrtisoruuoupldwq.supabase.co

API Key anon_public : eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1lYW9ycnRpc29ydXVvdXBsZHdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNzQ3ODgsImV4cCI6MjA2Nzg1MDc4OH0.YERbS4mDoNhaxvF5fHNoQfE4bjvaxqptcN-11cSZjHM


import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://meaorrtisoruuoupldwq.supabase.co'
const supabaseKey = process.env.SUPABASE_KEY
const supabase = createClient(supabaseUrl, supabaseKey) 