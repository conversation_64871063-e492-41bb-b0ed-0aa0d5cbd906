/**
 * Activation Service for iCalDZ
 * 
 * This service handles:
 * - Online activation verification with Supabase
 * - Device ID generation and validation
 * - Local license storage with encryption
 * - Offline license validation
 */

import { machineIdSync } from 'node-machine-id';
import CryptoJS from 'crypto-js';
import Store from 'electron-store';
import { supabase, TABLES, ACTIVATION_STATUS } from '../config/supabase.js';
import { securityService } from './SecurityService.js';

class ActivationService {
  constructor() {
    this.store = new Store();
    this.secretKey = 'iCalDZ-2025-Activation-Secret-Key-v1.0';
    this.licenseKey = 'icaldz-license-data';
    this.deviceId = null;
    this.isActivated = false;
    this.activationData = null;
  }

  /**
   * Initialize the activation service
   */
  async initialize() {
    try {
      // Initialize security service first
      await securityService.initialize();

      // Get device ID
      this.deviceId = this.getDeviceId();

      // Validate security session
      securityService.validateSession();

      // Check for existing activation
      await this.checkLocalActivation();

      console.log('🔐 Activation Service initialized');
      console.log(`📱 Device ID: ${this.deviceId}`);
      console.log(`✅ Activated: ${this.isActivated}`);

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize activation service:', error);
      return false;
    }
  }

  /**
   * Get unique device ID
   */
  getDeviceId() {
    try {
      return machineIdSync();
    } catch (error) {
      console.error('❌ Failed to get device ID:', error);
      // Fallback to a combination of available system info
      const fallbackId = `${navigator.platform}-${navigator.userAgent.length}-${Date.now()}`;
      return CryptoJS.SHA256(fallbackId).toString();
    }
  }

  /**
   * Activate the application with an activation code
   */
  async activate(activationCode, userName = '') {
    try {
      console.log('🔄 Starting activation process...');
      
      // Validate activation code format
      if (!this.isValidCodeFormat(activationCode)) {
        throw new Error('Invalid activation code format');
      }

      // Check with Supabase
      const { data, error } = await supabase
        .from(TABLES.ACTIVATIONS)
        .select('*')
        .eq('activation_code', activationCode.trim().toUpperCase())
        .single();

      if (error) {
        console.error('❌ Supabase query error:', error);
        throw new Error('Failed to verify activation code');
      }

      if (!data) {
        throw new Error('Activation code not found');
      }

      // Check if code is unused
      if (data.status !== ACTIVATION_STATUS.UNUSED) {
        if (data.status === ACTIVATION_STATUS.ACTIVATED) {
          // Check if it's the same device
          if (data.device_id === this.deviceId) {
            console.log('✅ Code already activated on this device');
            await this.saveLocalActivation(data);
            return { success: true, message: 'Already activated on this device' };
          } else {
            throw new Error('Activation code already used on another device');
          }
        } else if (data.status === ACTIVATION_STATUS.BLOCKED) {
          throw new Error('Activation code is blocked');
        }
      }

      // Update activation in Supabase
      const { error: updateError } = await supabase
        .from(TABLES.ACTIVATIONS)
        .update({
          device_id: this.deviceId,
          status: ACTIVATION_STATUS.ACTIVATED,
          activated_at: new Date().toISOString(),
          user_name: userName || null,
          updated_at: new Date().toISOString()
        })
        .eq('activation_code', activationCode.trim().toUpperCase());

      if (updateError) {
        console.error('❌ Failed to update activation:', updateError);
        throw new Error('Failed to complete activation');
      }

      // Save activation locally
      const activationData = {
        ...data,
        device_id: this.deviceId,
        status: ACTIVATION_STATUS.ACTIVATED,
        activated_at: new Date().toISOString(),
        user_name: userName || null
      };

      await this.saveLocalActivation(activationData);

      console.log('✅ Activation successful!');
      return { 
        success: true, 
        message: 'Activation successful',
        data: activationData 
      };

    } catch (error) {
      console.error('❌ Activation failed:', error);
      return { 
        success: false, 
        message: error.message || 'Activation failed' 
      };
    }
  }

  /**
   * Save activation data locally with encryption
   */
  async saveLocalActivation(activationData) {
    try {
      // Get security fingerprint
      const securityStatus = securityService.getSecurityStatus();

      const dataToSave = {
        activation_code: activationData.activation_code,
        device_id: this.deviceId,
        activated: true,
        activated_at: activationData.activated_at,
        user_name: activationData.user_name,
        status: activationData.status,
        saved_at: new Date().toISOString(),
        security_fingerprint: securityStatus.hardwareFingerprint,
        security_version: '1.0'
      };

      // Create multiple layers of encryption
      const firstEncryption = CryptoJS.AES.encrypt(
        JSON.stringify(dataToSave),
        this.secretKey
      ).toString();

      const secondEncryption = CryptoJS.AES.encrypt(
        firstEncryption,
        this.deviceId + this.secretKey
      ).toString();

      // Save to electron-store
      this.store.set(this.licenseKey, secondEncryption);

      // Update internal state
      this.isActivated = true;
      this.activationData = dataToSave;

      console.log('💾 Activation data saved locally with enhanced security');
      return true;
    } catch (error) {
      console.error('❌ Failed to save activation locally:', error);
      return false;
    }
  }

  /**
   * Check local activation status
   */
  async checkLocalActivation() {
    try {
      const encryptedData = this.store.get(this.licenseKey);

      if (!encryptedData) {
        console.log('📝 No local activation found');
        this.isActivated = false;
        return false;
      }

      // Decrypt with multiple layers
      try {
        // First decryption layer
        const firstDecryption = CryptoJS.AES.decrypt(
          encryptedData,
          this.deviceId + this.secretKey
        );
        const firstDecryptedString = firstDecryption.toString(CryptoJS.enc.Utf8);

        // Second decryption layer
        const secondDecryption = CryptoJS.AES.decrypt(firstDecryptedString, this.secretKey);
        const decryptedData = JSON.parse(secondDecryption.toString(CryptoJS.enc.Utf8));

        // Validate the activation data
        if (!this.validateLocalActivation(decryptedData)) {
          console.log('❌ Local activation validation failed');
          this.clearLocalActivation();
          return false;
        }

        this.isActivated = true;
        this.activationData = decryptedData;
        console.log('✅ Valid local activation found');
        return true;

      } catch (decryptError) {
        // Try legacy single-layer decryption for backward compatibility
        console.log('🔄 Trying legacy decryption...');
        const legacyDecryption = CryptoJS.AES.decrypt(encryptedData, this.secretKey);
        const legacyData = JSON.parse(legacyDecryption.toString(CryptoJS.enc.Utf8));

        if (this.validateLocalActivation(legacyData)) {
          // Re-save with new encryption
          await this.saveLocalActivation(legacyData);
          this.isActivated = true;
          this.activationData = legacyData;
          console.log('✅ Legacy activation upgraded');
          return true;
        } else {
          throw decryptError;
        }
      }

    } catch (error) {
      console.error('❌ Failed to check local activation:', error);
      this.clearLocalActivation();
      return false;
    }
  }

  /**
   * Validate local activation data
   */
  validateLocalActivation(data) {
    try {
      // Check required fields
      if (!data.activation_code || !data.device_id || !data.activated) {
        console.log('❌ Missing required activation fields');
        return false;
      }

      // Check device ID matches
      if (data.device_id !== this.deviceId) {
        console.log('❌ Device ID mismatch');
        return false;
      }

      // Check activation status
      if (!data.activated || data.status !== ACTIVATION_STATUS.ACTIVATED) {
        console.log('❌ Invalid activation status');
        return false;
      }

      // Enhanced security validation
      if (data.security_fingerprint) {
        const currentFingerprint = securityService.getSecurityStatus().hardwareFingerprint;
        if (data.security_fingerprint !== currentFingerprint) {
          console.log('❌ Security fingerprint mismatch');
          return false;
        }
      }

      // Check activation age (optional security measure)
      if (data.activated_at) {
        const activationDate = new Date(data.activated_at);
        const now = new Date();
        const daysSinceActivation = (now - activationDate) / (1000 * 60 * 60 * 24);

        // Log suspicious old activations (but don't fail validation)
        if (daysSinceActivation > 365) {
          console.warn('⚠️ Very old activation detected:', daysSinceActivation, 'days');
        }
      }

      return true;
    } catch (error) {
      console.error('❌ Validation error:', error);
      return false;
    }
  }

  /**
   * Clear local activation data
   */
  clearLocalActivation() {
    try {
      this.store.delete(this.licenseKey);
      this.isActivated = false;
      this.activationData = null;
      console.log('🗑️ Local activation cleared');
    } catch (error) {
      console.error('❌ Failed to clear activation:', error);
    }
  }

  /**
   * Validate activation code format
   */
  isValidCodeFormat(code) {
    if (!code || typeof code !== 'string') return false;

    // Expected formats:
    // ICAL-YYYY-XXXX-XXXX-XXXX (lifetime)
    // TRIAL3-YYYY-XXXX-XXXX-XXXX (3-day trial)
    // TRIAL7-YYYY-XXXX-XXXX-XXXX (7-day trial)
    // TRIAL30-YYYY-XXXX-XXXX-XXXX (30-day trial)
    const patterns = [
      /^ICAL-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i,
      /^TRIAL3-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i,
      /^TRIAL7-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i,
      /^TRIAL30-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i
    ];

    return patterns.some(pattern => pattern.test(code.trim().toUpperCase()));
  }

  /**
   * Get activation status
   */
  getActivationStatus() {
    return {
      isActivated: this.isActivated,
      deviceId: this.deviceId,
      activationData: this.activationData
    };
  }

  /**
   * Check if app should be allowed to run
   */
  isAppAuthorized() {
    return this.isActivated && this.activationData;
  }
}

// Create singleton instance
export const activationService = new ActivationService();

// Export default
export default activationService;
