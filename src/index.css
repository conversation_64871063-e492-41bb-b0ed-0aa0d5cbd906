:root {
  --primary-color: #16a085;
  --secondary-color: #64748b;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --info-color: #3498db;
  --background-color: #ecf0f1;
  --card-background: #ffffff;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --border-color: #bdc3c7;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 20px rgba(0, 0, 0, 0.15);
  --sidebar-bg: #16a085;
  --sidebar-hover: #138d75;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Inter', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  direction: rtl; /* Default, will be overridden by language selection */
}

/* Language-specific body styles */
body.lang-ar {
  direction: rtl;
  font-family: 'Cairo', '<PERSON><PERSON>', 'Noto Sans Arabic', sans-serif;
}

body.lang-fr,
body.lang-en {
  direction: ltr;
  font-family: 'Inter', 'Roboto', 'Segoe UI', sans-serif;
}

/* Language-specific purchase page styling */
.lang-fr .purchases-page,
.lang-en .purchases-page {
  /* LTR layout for French and English */
}

/* Language Selection Dialog Styles */
.language-selection-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

.language-selection-dialog {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.4s ease-out;
}

.language-header {
  text-align: center;
  margin-bottom: 2rem;
}

.language-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.language-header h2 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  line-height: 1.4;
}

.language-header p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
}

.language-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.language-option:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.15);
  transform: translateY(-2px);
}

.language-option.selected {
  border-color: var(--primary-color);
  background: rgba(22, 160, 133, 0.05);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.2);
}

.language-flag {
  font-size: 2rem;
  min-width: 40px;
}

.language-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.language-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.language-direction {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.language-check {
  color: var(--primary-color);
  font-size: 1.5rem;
  font-weight: bold;
}

.language-actions {
  text-align: center;
  margin-bottom: 1.5rem;
}

.confirm-language-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin: 0 auto;
}

.confirm-language-btn:hover:not(:disabled) {
  background: var(--sidebar-hover);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(22, 160, 133, 0.3);
}

.confirm-language-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

.language-footer {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.language-footer small {
  color: var(--text-secondary);
  font-size: 0.75rem;
  line-height: 1.4;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Login Styles */
.login-container {
  min-height: 100vh;
  display: grid;
  grid-template-columns: 1fr 1fr;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
}

.login-card {
  background: white;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 400px;
  margin: auto;
  border-radius: 20px;
  box-shadow: var(--shadow-lg);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-avatar {
  width: 80px;
  height: 80px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  margin: 0 auto 1rem;
}

.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: 10px;
  font-size: 1rem;
  font-family: inherit;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.login-btn {
  width: 100%;
  padding: 1rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-btn:hover {
  background: var(--sidebar-hover);
}

.login-footer {
  margin-top: 2rem;
  text-align: center;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.login-info {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  padding: 2rem;
}

.system-info {
  text-align: center;
  max-width: 500px;
}

.system-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.system-info h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.system-info p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.features-list {
  text-align: right;
}

.feature {
  margin-bottom: 1rem;
  font-size: 1.1rem;
  opacity: 0.95;
}

/* Accounting System Styles */
.accounting-system {
  display: flex;
  min-height: 100vh;
  background-color: var(--background-color);
}

.sidebar {
  width: 280px;
  background: var(--sidebar-bg);
  color: white;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-lg);
  position: fixed;
  top: 0;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
  transition: all 0.3s ease;
}

/* Dynamic Sidebar Colors Based on Selected Page */
.sidebar.page-dashboard {
  background: linear-gradient(135deg, #177e89, #20c997) !important;
}

.sidebar.page-sales {
  background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.sidebar.page-repairs {
  background: linear-gradient(135deg, #00b9ae, #037171) !important;
}

.sidebar.page-customers {
  background: linear-gradient(135deg, #6f42c1, #e83e8c) !important;
}

.sidebar.page-inventory {
  background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
}

.sidebar.page-purchases {
  background: linear-gradient(135deg, #007bff, #6610f2) !important;
}

.sidebar.page-reports {
  background: linear-gradient(135deg, #8e44ad, #9b59b6) !important;
}

.sidebar.page-settings {
  background: linear-gradient(135deg, #fd7e14, #ffc107) !important;
}

/* Repair Management Styles - Modern Teal/Green Theme */
.repairs-page {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, #a9f9f4, #b9fff9);
  min-height: 100vh;
}

/* Repair Page Header - Modern Teal Design */
.repairs-page .page-header {
  background: linear-gradient(135deg, #00b9ae, #037171) !important;
  color: white !important;
  padding: 2rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 185, 174, 0.3);
}

.repairs-page .page-header h1 {
  color: white !important;
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.repairs-page .page-header p {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 1.1rem;
  margin: 0.5rem 0 0 0;
}

.repair-action-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin: 2rem 0;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 2px solid #00b9ae;
}

@media (max-width: 1200px) {
  .repair-action-buttons {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .repair-action-buttons {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }
}

.repair-action-btn {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
  border: 3px solid #00b9ae;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-family: inherit;
  min-height: 120px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #a9f9f4, #b9fff9);
}

.repair-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(0, 185, 174, 0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.repair-action-btn:hover::before {
  transform: translateX(100%);
}

.repair-action-new {
  background: linear-gradient(135deg, #00b9ae, #037171);
  color: white;
  border-color: #037171;
}

.repair-action-completed {
  background: linear-gradient(135deg, #00b9ae, #037171);
  color: white;
  border-color: #037171;
}

.repair-action-pickup {
  background: linear-gradient(135deg, #00b9ae, #037171);
  color: white;
  border-color: #037171;
}

.repair-action-btn:hover {
  transform: translateY(-6px);
  box-shadow: 0 16px 48px rgba(0, 185, 174, 0.4);
  border-color: #03312e;
}

.repair-action-new:hover {
  background: linear-gradient(135deg, #037171, #03312e);
}

.repair-action-completed:hover {
  background: linear-gradient(135deg, #037171, #03312e);
}

.repair-action-pickup:hover {
  background: linear-gradient(135deg, #037171, #03312e);
}

.action-btn-icon {
  font-size: 3rem;
  opacity: 0.9;
  flex-shrink: 0;
}

.action-btn-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.4rem;
  font-weight: 700;
  font-family: 'Cairo', sans-serif;
}

.action-btn-content p {
  margin: 0;
  font-size: 1rem;
  opacity: 0.9;
  line-height: 1.4;
}

/* Arabic RTL Support for Repair Buttons */
.lang-ar .repair-action-btn {
  text-align: right;
  flex-direction: row-reverse;
}

.lang-ar .action-btn-content h3,
.lang-ar .action-btn-content p {
  font-family: 'Cairo', sans-serif;
  font-weight: 700;
}

/* Repair Table Section - Modern Amber Design */
.repair-table-section {
  margin-top: 2rem;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.repair-table-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #00b9ae, #037171);
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 185, 174, 0.3);
}

.repair-table-section .section-header h2 {
  margin: 0;
  color: white !important;
  font-size: 1.8rem;
  font-weight: 800;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Modern Repair Table Design */
.repairs-page .data-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
}

.repairs-page .data-table thead {
  background: linear-gradient(135deg, #00b9ae, #037171);
}

.repairs-page .data-table thead th {
  background: transparent;
  color: white !important;
  font-weight: 700;
  font-size: 1rem;
  padding: 1.2rem 1rem;
  text-align: center;
  border: none;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.repairs-page .data-table tbody tr {
  border-bottom: 1px solid #f8f9fa;
  transition: all 0.3s ease;
}

.repairs-page .data-table tbody tr:hover {
  background: linear-gradient(135deg, #a9f9f4, #b9fff9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 185, 174, 0.15);
}

.repairs-page .data-table tbody td {
  padding: 1rem;
  font-weight: 600;
  font-size: 1rem;
  text-align: center;
  vertical-align: middle;
  border: none;
  max-width: 200px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Text wrapping for long content - break after 3 words */
.repairs-page .data-table tbody td.long-text {
  white-space: normal;
  line-height: 1.4;
}

.repairs-page .data-table tbody td.long-text::after {
  content: "";
  display: inline-block;
  width: 0;
  word-break: break-word;
}

/* QR Code Column Styling */
.qr-code-cell {
  width: 100px;
  text-align: center;
}

.qr-code-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  border: 2px solid #00b9ae;
  box-shadow: 0 2px 8px rgba(0, 185, 174, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.qr-code-image:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(0, 185, 174, 0.5);
}

/* Modern Table Filters - Teal Theme */
.repairs-page .table-filters {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #a9f9f4, #b9fff9);
  border-radius: 12px;
  border: 2px solid #00b9ae;
  box-shadow: 0 4px 16px rgba(0, 185, 174, 0.2);
}

.repairs-page .filter-input,
.repairs-page .filter-select {
  padding: 0.8rem 1.2rem;
  border: 2px solid #00b9ae;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  background: white;
  color: #333;
  transition: all 0.3s ease;
  min-width: 200px;
}

.repairs-page .filter-input:focus,
.repairs-page .filter-select:focus {
  outline: none;
  border-color: #037171;
  box-shadow: 0 0 0 3px rgba(0, 185, 174, 0.2);
  transform: translateY(-1px);
}

.repairs-page .filter-input::placeholder {
  color: #999;
  font-weight: 500;
}

/* Modern Action Buttons in Table */
.repairs-page .action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
}

.repairs-page .action-buttons .btn {
  padding: 0.5rem 0.8rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.repairs-page .action-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.repairs-page .action-buttons .btn-info {
  background: linear-gradient(135deg, #17a2b8, #20c997);
  color: white;
}

.repairs-page .action-buttons .btn-warning {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: white;
}

.repairs-page .action-buttons .btn-secondary {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
}

.repairs-page .action-buttons .btn-primary {
  background: linear-gradient(135deg, #007bff, #6610f2);
  color: white;
}

/* QR Code Display Modal */
.qr-display-modal {
  max-width: 600px;
  width: 90%;
}

.qr-display-modal .modal-header,
.qr-display-modal .modal-header-ltr {
  background: linear-gradient(135deg, #00b9ae, #037171) !important;
  color: white !important;
  padding: 2rem;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 8px 32px rgba(0, 185, 174, 0.3);
  position: relative;
  overflow: hidden;
}

.qr-display-modal .modal-header h2,
.qr-display-modal .modal-header-ltr h2 {
  color: white !important;
  font-size: 2rem !important;
  font-weight: 800 !important;
  margin: 0 !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.qr-display-container {
  padding: 2rem;
  text-align: center;
  background: linear-gradient(135deg, #a9f9f4, #b9fff9);
  min-height: 300px;
}

.qr-code-section {
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #e0f7f4, #b3f0e8);
  border-radius: 16px;
  border: 3px solid #00b9ae;
}

.large-qr-code {
  width: 250px;
  height: 250px;
  border-radius: 12px;
  border: 3px solid #00b9ae;
  box-shadow: 0 8px 32px rgba(0, 185, 174, 0.3);
}

.repair-details-section {
  margin-bottom: 2rem;
  text-align: left;
}

.repair-details-section h3 {
  color: #00b9ae;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: center;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #00b9ae;
}

.detail-label {
  font-weight: 600;
  color: #666;
  font-size: 0.9rem;
}

.detail-value {
  font-weight: 700;
  color: #333;
  font-size: 1.1rem;
}

.qr-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.qr-actions .btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.qr-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* QR Loading Animation */
.qr-loading {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* Enhanced Repair Completion Modal */
.repair-completion-modal {
  max-width: 1000px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
}

.repair-completion-modal .modal-header {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  padding: 2rem;
  border-radius: 16px 16px 0 0;
}

.repair-completion-modal .modal-header h2 {
  color: white !important;
  font-size: 2rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
}

.repair-completion-modal .modal-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin: 0;
}

.repair-completion-container {
  padding: 2rem;
  background: linear-gradient(135deg, #fff8e1, #ffeaa7);
}

.completion-question {
  text-align: center;
  margin-bottom: 2rem;
}

.completion-question h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #00b9ae;
  margin: 0;
}

/* Client Selection Section */
.client-selection-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  border: 3px solid #00b9ae;
  box-shadow: 0 8px 32px rgba(0, 185, 174, 0.2);
  margin-bottom: 2rem;
}

.client-selection-section h3 {
  color: #00b9ae;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-align: center;
}

.repairs-list {
  display: grid;
  gap: 1rem;
  max-height: 300px;
  overflow-y: auto;
}

.repair-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border: 2px solid #00b9ae;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.repair-item:hover {
  background: linear-gradient(135deg, #a9f9f4, #b9fff9);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 185, 174, 0.3);
}

.repair-info h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-weight: 700;
}

.repair-info p {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.repair-price {
  font-weight: 700;
  color: #00b9ae;
  font-size: 1.1rem;
}

.select-btn {
  font-size: 1.5rem;
  color: #00b9ae;
  font-weight: bold;
}

.selected-repair-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #00b9ae, #037171);
  color: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.selected-repair-info h3 {
  color: white !important;
  margin: 0;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.completion-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.completion-option {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  border: 3px solid #00b9ae;
  box-shadow: 0 8px 32px rgba(0, 185, 174, 0.2);
  transition: all 0.3s ease;
}

.completion-option:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 48px rgba(0, 185, 174, 0.3);
}

.option-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #00b9ae;
}

.option-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: linear-gradient(135deg, #00b9ae, #037171);
  color: white;
  box-shadow: 0 4px 16px rgba(0, 185, 174, 0.3);
}

.option-header h4 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.success-option .option-icon {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.failure-option .option-icon {
  background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.option-form .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.option-form .form-group {
  margin-bottom: 1.5rem;
}

.option-form .form-group.full-width {
  grid-column: 1 / -1;
}

.option-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
  font-size: 1rem;
}

.modern-input,
.modern-select,
.modern-textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #00b9ae;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  background: white;
  transition: all 0.3s ease;
}

.modern-input:focus,
.modern-select:focus,
.modern-textarea:focus {
  outline: none;
  border-color: #037171;
  box-shadow: 0 0 0 3px rgba(0, 185, 174, 0.2);
  transform: translateY(-1px);
}

.modern-btn {
  width: 100%;
  padding: 1.2rem 2rem;
  font-size: 1.1rem;
  font-weight: 700;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* Scanner and Search Row Layout */
.scanner-search-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Barcode Scanner Section */
.barcode-scanner-section {
  padding: 2rem;
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(70, 172, 194, 0.3);
}

/* Search Filter Section */
.search-filter-section {
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16px;
  border: 2px solid #46ACC2;
}

.scanner-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.scanner-icon-large {
  font-size: 3rem;
  color: white;
  opacity: 0.9;
}

.scanner-info h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.scanner-info p {
  margin: 0;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

.barcode-input-container {
  position: relative;
}

.barcode-scanner-input {
  width: 100%;
  padding: 1rem 3rem 1rem 1.5rem;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.95);
  color: #2c3e50;
  transition: all 0.3s ease;
}

.barcode-scanner-input:focus {
  outline: none;
  border-color: white;
  background: white;
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.3);
}

.barcode-scanner-input::placeholder {
  color: #6c757d;
  font-weight: 500;
}

.barcode-scanner-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.5rem;
  color: #46ACC2;
}

/* Search Filter Section */
.search-filter-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  border: 2px solid #46ACC2;
}

.filter-header h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.search-filters {
  display: flex;
  gap: 1rem;
}

.filter-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #46ACC2;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.filter-input:focus {
  outline: none;
  border-color: #3a8fa3;
  box-shadow: 0 0 0 3px rgba(70, 172, 194, 0.2);
}

/* Repair Search Section */
.repair-search-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  border: 2px solid #46ACC2;
}

.search-controls {
  display: flex;
  gap: 2rem;
  align-items: center;
  justify-content: space-between;
}

.search-input-group {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 2px solid #46ACC2;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3a8fa3;
  box-shadow: 0 0 0 3px rgba(70, 172, 194, 0.2);
}

.search-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #46ACC2;
  font-size: 1.2rem;
}

.barcode-scanner-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.barcode-scanner-group:hover {
  background: linear-gradient(135deg, #3a8fa3, #2e7282);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(70, 172, 194, 0.3);
}

.scanner-icon {
  font-size: 1.2rem;
}

/* Repairs Table */
.repairs-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid #46ACC2;
}

.repairs-table {
  width: 100%;
  border-collapse: collapse;
}

.repairs-table thead {
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  color: white;
}

.repairs-table th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.repairs-table tbody tr {
  border-bottom: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.repairs-table tbody tr:hover,
.clickable-row:hover {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  transform: scale(1.01);
  cursor: pointer;
}

.clickable-row {
  transition: all 0.3s ease;
}

.clickable-row:hover {
  box-shadow: 0 4px 16px rgba(70, 172, 194, 0.2);
}

/* Pickup Barcode Scanner Section */
.pickup-barcode-section {
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(66, 242, 247, 0.3);
}

.pickup-filter-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  border: 2px solid #42F2F7;
}

.pickup-repairs-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid #42F2F7;
}

.pickup-repairs-table {
  width: 100%;
  border-collapse: collapse;
}

.pickup-repairs-table thead {
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
}

.pickup-repair-row:hover {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  cursor: pointer;
}

.status-cell {
  text-align: center;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-waitingForClient {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.status-notSuccess {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

/* Récupération Client Fixed Table */
.recuperation-table-fixed {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid #42F2F7;
  height: 400px;
}

.recuperation-fixed-table {
  width: 100%;
  border-collapse: collapse;
}

.recuperation-fixed-table thead {
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
}

/* Réviser et Finaliser Section */
.reviser-finaliser-section {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 3px solid #42F2F7;
  box-shadow: 0 12px 40px rgba(66, 242, 247, 0.15);
}

.ltr-layout {
  direction: ltr;
  text-align: left;
}

.rtl-layout {
  direction: rtl;
  text-align: right;
}

.rtl-layout .first-row-grid {
  direction: rtl;
}

.rtl-layout .info-item {
  text-align: right;
}

.rtl-layout .second-row-grid {
  direction: rtl;
}

.rtl-layout .prix-final-section,
.rtl-layout .detail-prix-section {
  direction: rtl;
  text-align: right;
}

.rtl-layout .price-item-compact {
  direction: rtl;
  flex-direction: row-reverse;
}

.rtl-layout .third-row-actions {
  direction: rtl;
}

.rtl-layout .btn-terminer-recuperation,
.rtl-layout .btn-print-invoice {
  direction: ltr; /* Keep button content LTR for icons */
}

/* First Row - Horizontal Grid */
.first-row-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #e9ecef;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
  word-wrap: break-word;
}

/* Second Row - Prix Final and Détail des Prix */
.second-row-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.prix-final-section {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 2rem;
  border-radius: 16px;
  border: 2px solid #42F2F7;
}

.detail-prix-section {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 2rem;
  border-radius: 16px;
  border: 2px solid #42F2F7;
}

.detail-prix-section h4 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 700;
}

.price-breakdown-compact {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.price-item-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #dee2e6;
  font-size: 1.1rem;
}

.price-item-compact:last-child {
  border-bottom: none;
}

.total-compact {
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 700;
  font-size: 1.2rem;
}

/* Third Row - Action Buttons */
.third-row-actions {
  display: flex;
  gap: 2rem;
  justify-content: center;
}

.btn-terminer-recuperation,
.btn-print-invoice {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 2.5rem;
  border: none;
  border-radius: 16px;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 250px;
  justify-content: center;
}

.btn-terminer-recuperation {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
}

.btn-terminer-recuperation:hover {
  background: linear-gradient(135deg, #20c997, #17a2b8);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.btn-print-invoice {
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
  box-shadow: 0 6px 20px rgba(111, 66, 193, 0.3);
}

.btn-print-invoice:hover {
  background: linear-gradient(135deg, #5a32a3, #4c2a85);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(111, 66, 193, 0.4);
}

/* Responsive for Réviser et Finaliser */
@media (max-width: 1024px) {
  .first-row-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .second-row-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .first-row-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .scanner-search-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .third-row-actions {
    flex-direction: column;
    gap: 1.5rem;
  }

  .btn-terminer-recuperation,
  .btn-print-invoice {
    min-width: 100%;
  }

  .repairs-table-container-fixed,
  .reparation-table-fixed,
  .recuperation-table-fixed {
    height: 300px; /* Smaller height on mobile */
  }
}

@media (max-width: 480px) {
  .first-row-grid {
    grid-template-columns: 1fr;
  }

  .info-item {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
  }
}

/* Modern Repair Summary Card */
.modern-repair-summary-card {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 2rem;
  border: 3px solid #42F2F7;
  box-shadow: 0 12px 40px rgba(66, 242, 247, 0.15);
  transition: all 0.3s ease;
}

.client-header-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 3px solid #e9ecef;
}

.client-avatar-large {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  font-weight: 700;
  box-shadow: 0 6px 20px rgba(66, 242, 247, 0.3);
}

.client-details-large {
  flex: 1;
}

.client-name-large {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
}

.client-phone-large {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  color: #6c757d;
  font-weight: 500;
}

.device-info-large {
  margin: 0;
  font-size: 1.1rem;
  color: #495057;
  font-weight: 600;
}

.repair-status-badge {
  text-align: center;
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(66, 242, 247, 0.3);
}

.status-label {
  display: block;
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.status-value {
  display: block;
  font-size: 1.2rem;
  font-weight: 700;
  text-transform: uppercase;
}

.repair-id-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 1.5rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  border: 2px solid #42F2F7;
}

.id-icon {
  font-size: 2.5rem;
  color: #42F2F7;
}

.id-details {
  flex: 1;
}

.id-label {
  display: block;
  font-size: 1rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.id-value-large {
  display: block;
  font-size: 1.5rem;
  color: #2c3e50;
  font-weight: 700;
  font-family: 'Courier New', monospace;
}

.details-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.detail-card {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.detail-card:hover {
  border-color: #42F2F7;
  box-shadow: 0 8px 24px rgba(66, 242, 247, 0.15);
  transform: translateY(-2px);
}

.detail-icon-large {
  font-size: 2rem;
  color: #42F2F7;
  margin-top: 0.25rem;
}

.detail-content-large {
  flex: 1;
}

.detail-label-large {
  display: block;
  font-size: 1rem;
  color: #6c757d;
  margin-bottom: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value-large {
  display: block;
  font-size: 1.2rem;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.4;
}

.pricing-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16px;
  padding: 2rem;
  border: 2px solid #42F2F7;
  margin-bottom: 2rem;
}

.pricing-header h4 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 700;
  text-align: center;
}

.price-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #dee2e6;
}

.price-row:last-child {
  border-bottom: none;
}

.price-label-clean {
  font-weight: 600;
  color: #495057;
  font-size: 1.1rem;
}

.price-value-clean {
  font-weight: 700;
  color: #28a745;
  font-size: 1.2rem;
}

.total-row {
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-top: 1rem;
  border-bottom: none;
}

.price-label-total,
.price-value-total {
  color: white;
  font-weight: 700;
  font-size: 1.3rem;
}

/* Modern Completion Actions */
.modern-completion-actions {
  display: flex;
  gap: 2rem;
  justify-content: center;
  margin-top: 2rem;
}

.btn-complete-modern,
.btn-print-modern {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 2.5rem;
  border: none;
  border-radius: 16px;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  justify-content: center;
}

.btn-complete-modern {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
}

.btn-complete-modern:hover {
  background: linear-gradient(135deg, #20c997, #17a2b8);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.btn-print-modern {
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
  box-shadow: 0 6px 20px rgba(111, 66, 193, 0.3);
}

.btn-print-modern:hover {
  background: linear-gradient(135deg, #5a32a3, #4c2a85);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(111, 66, 193, 0.4);
}

.btn-icon-large {
  font-size: 1.5rem;
}

.btn-text-large {
  font-weight: 700;
}

/* Responsive for modern card */
@media (max-width: 768px) {
  .client-header-section {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .details-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .modern-completion-actions {
    flex-direction: column;
    gap: 1.5rem;
  }

  .client-avatar-large {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .client-name-large {
    font-size: 1.5rem;
  }
}

.repairs-table td {
  padding: 1rem;
  vertical-align: top;
}

.client-cell .client-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.client-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.client-phone {
  font-size: 0.85rem;
  color: #6c757d;
}

.device-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.device-name {
  font-weight: 500;
  color: #2c3e50;
}

.device-type {
  font-size: 0.85rem;
  color: #6c757d;
  text-transform: capitalize;
}

.problem-desc {
  color: #495057;
  line-height: 1.4;
  max-width: 200px;
}

.repair-price {
  font-weight: 600;
  color: #28a745;
  font-size: 1.1rem;
}

.deposit-date {
  color: #6c757d;
  font-size: 0.9rem;
}

.btn-select-repair {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-select-repair:hover {
  background: linear-gradient(135deg, #20c997, #17a2b8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-select-repair .btn-icon {
  font-size: 1rem;
}

/* Fixed Table Layout for 3-Row Display */
.repairs-table-container-fixed,
.reparation-table-fixed {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid #46ACC2;
  height: 400px; /* Fixed height for 3 rows */
}

.table-wrapper-fixed {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-header-fixed {
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-header-hidden {
  visibility: hidden;
  height: 0;
}

.table-body-scrollable {
  flex: 1;
  overflow-y: auto; /* Vertical scroll for table content */
  overflow-x: auto; /* Horizontal scroll for wide tables */
  max-height: calc(100% - 60px); /* Subtract header height */
  scrollbar-width: thin;
  scrollbar-color: #498C8A #f8f9fa;
}

/* Enhanced vertical scrolling */
.table-body-scrollable::-webkit-scrollbar {
  width: 6px;
  height: 8px;
}

.table-body-scrollable::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

.table-body-scrollable::-webkit-scrollbar-thumb {
  background: #498C8A;
  border-radius: 3px;
}

.table-body-scrollable::-webkit-scrollbar-thumb:hover {
  background: #3a7270;
}

/* Horizontal scroll for table containers */
.table-container {
  overflow-x: auto;
  overflow-y: visible;
}

/* Smooth horizontal scrolling */
.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #498C8A;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #3a7270;
}

.reparation-fixed-table {
  width: 100%;
  border-collapse: collapse;
}

.reparation-fixed-table th,
.reparation-fixed-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.reparation-fixed-table th {
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Hide scrollbar but keep functionality */
.table-body-scrollable::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.table-body-scrollable {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Centered Action Buttons */
.button-center-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 1.5rem;
}

.centered-action-btn {
  min-width: 200px;
  max-width: 300px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.centered-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.centered-action-btn .btn-icon {
  font-size: 1.2rem;
}

.centered-action-btn .btn-text {
  font-weight: 600;
}

/* Récupération Client Search Methods */
.search-methods {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 2rem;
}

.methods-ltr {
  direction: ltr;
  text-align: left;
}

.methods-rtl {
  direction: rtl;
  text-align: right;
}

.search-method {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #42F2F7;
  box-shadow: 0 4px 20px rgba(66, 242, 247, 0.1);
  transition: all 0.3s ease;
}

.search-method:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(66, 242, 247, 0.2);
  border-color: #36c7cc;
}

.method-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.method-icon {
  font-size: 2rem;
  color: #42F2F7;
}

.method-info h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.method-info p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.barcode-search-form,
.manual-search-form {
  position: relative;
}

.barcode-search-form input,
.manual-search-form input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 2px solid #42F2F7;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.barcode-search-form input:focus,
.manual-search-form input:focus {
  outline: none;
  border-color: #36c7cc;
  box-shadow: 0 0 0 3px rgba(66, 242, 247, 0.2);
}

.barcode-icon,
.search-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #42F2F7;
  font-size: 1.2rem;
  pointer-events: none; /* Prevent icon from blocking cursor focus */
  z-index: 1;
}

.list-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #42F2F7;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.list-select:focus {
  outline: none;
  border-color: #36c7cc;
  box-shadow: 0 0 0 3px rgba(66, 242, 247, 0.2);
}

/* Responsive for search methods */
@media (max-width: 1024px) {
  .search-methods {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .search-method {
    padding: 1rem;
  }

  .method-header {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .method-icon {
    font-size: 1.5rem;
  }
}

/* Enhanced Repair Summary Card */
.repair-summary-card {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 3px solid #42F2F7;
  box-shadow: 0 8px 32px rgba(66, 242, 247, 0.15);
  transition: all 0.3s ease;
}

.repair-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(66, 242, 247, 0.2);
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #e9ecef;
}

.client-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  box-shadow: 0 4px 16px rgba(66, 242, 247, 0.3);
}

.client-details {
  flex: 1;
}

.client-details h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
}

.client-details p {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  color: #6c757d;
  font-weight: 500;
}

.repair-id {
  text-align: right;
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(66, 242, 247, 0.3);
}

.id-label {
  display: block;
  font-size: 0.8rem;
  opacity: 0.9;
  margin-bottom: 0.25rem;
}

.id-value {
  display: block;
  font-size: 1.1rem;
  font-weight: 700;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.detail-item:hover {
  border-color: #42F2F7;
  box-shadow: 0 4px 16px rgba(66, 242, 247, 0.1);
}

.detail-icon {
  font-size: 1.5rem;
  color: #42F2F7;
}

.detail-content {
  flex: 1;
}

.detail-label {
  display: block;
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.detail-value {
  display: block;
  font-size: 1rem;
  color: #2c3e50;
  font-weight: 600;
}

.pricing-summary {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #42F2F7;
}

.price-breakdown {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
}

.price-item:not(:last-child) {
  border-bottom: 1px solid #dee2e6;
}

.price-label {
  font-weight: 500;
  color: #495057;
}

.price-value {
  font-weight: 600;
  color: #28a745;
  font-size: 1.1rem;
}

.total-price-item {
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 0.5rem;
}

.total-price-item .price-label,
.total-price-item .price-value {
  color: white;
  font-weight: 700;
}

.total-amount {
  font-size: 1.3rem !important;
}

.completion-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  border: 2px solid #42F2F7;
  box-shadow: 0 4px 20px rgba(66, 242, 247, 0.1);
}

.final-price-adjustment {
  margin-bottom: 2rem;
}

.final-price-adjustment .modern-label {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.75rem;
}

.field-note {
  color: #6c757d;
  font-style: italic;
  margin-top: 0.5rem;
}

/* Responsive for repair summary */
@media (max-width: 768px) {
  .summary-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .repair-id {
    text-align: center;
  }

  .client-avatar {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
}

/* Responsive Design for Completion Modal */
@media (max-width: 768px) {
  .completion-options {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .option-form .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .repair-completion-modal {
    width: 98%;
    max-height: 95vh;
  }

  .repair-completion-container {
    padding: 1rem;
  }

  .search-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .search-input-group {
    max-width: 100%;
  }

  .repairs-table-container {
    overflow-x: auto;
  }

  .repairs-table {
    min-width: 800px;
  }
}

/* Enhanced Client Recovery Modal */
.client-recovery-modal {
  max-width: 1200px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
}

.client-recovery-modal .modal-header {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  padding: 2rem;
  border-radius: 16px 16px 0 0;
}

.client-recovery-modal .modal-header h2 {
  color: white !important;
  font-size: 2rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
}

.client-recovery-modal .modal-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin: 0;
}

.client-recovery-container {
  padding: 2rem;
  background: linear-gradient(135deg, #fff8e1, #ffeaa7);
}

.recovery-scanner-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 3px solid #f39c12;
  box-shadow: 0 8px 32px rgba(243, 156, 18, 0.2);
}

.scanner-visual {
  text-align: center;
  margin-bottom: 2rem;
}

.scanner-frame {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  padding: 2rem;
  border: 3px dashed #f39c12;
  border-radius: 16px;
  background: linear-gradient(135deg, #fff8e1, #ffeaa7);
}

.scanner-icon {
  font-size: 4rem;
  color: #f39c12;
}

.scanner-text h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.scanner-text p {
  font-size: 1rem;
  color: #666;
  margin: 0;
}

.scanner-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.modern-scanner-btn {
  padding: 1.5rem 3rem;
  font-size: 1.3rem;
  font-weight: 700;
  border-radius: 16px;
  border: none;
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(243, 156, 18, 0.3);
}

.modern-scanner-btn:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 36px rgba(243, 156, 18, 0.4);
}

.manual-search {
  width: 100%;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.repair-status-select {
  width: 100%;
  padding: 1rem;
  border: 2px solid #00b9ae;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  background: white;
  transition: all 0.3s ease;
}

.repair-status-select:focus {
  outline: none;
  border-color: #037171;
  box-shadow: 0 0 0 3px rgba(0, 185, 174, 0.2);
}

.repair-status-select optgroup {
  font-weight: 700;
  color: #00b9ae;
}

.repair-status-select option {
  padding: 0.5rem;
  font-weight: 500;
}

.manual-search-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #00b9ae;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.manual-search-input:focus {
  outline: none;
  border-color: #037171;
  box-shadow: 0 0 0 3px rgba(0, 185, 174, 0.2);
}

/* Recovery Summary */
.recovery-summary {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  border: 3px solid #f39c12;
  box-shadow: 0 8px 32px rgba(243, 156, 18, 0.2);
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f39c12;
}

.summary-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.summary-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-card {
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px solid #f39c12;
  overflow: hidden;
}

.summary-card.total-card {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, #fff8e1, #ffeaa7);
}

.card-header {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  padding: 1rem;
}

.card-header h4 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 700;
}

.card-content {
  padding: 1.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-item.total-item {
  font-size: 1.2rem;
  font-weight: 700;
  border: 2px solid #f39c12;
  padding: 1rem;
  border-radius: 8px;
  background: white;
}

.info-item .label {
  font-weight: 600;
  color: #666;
}

.info-item .value {
  font-weight: 700;
  color: #333;
}

.total-price {
  color: #f39c12 !important;
  font-size: 1.3rem !important;
}

/* Recovery Actions */
.recovery-actions {
  text-align: center;
}

.final-price-section {
  background: white;
  border: 2px solid #00b9ae;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.final-price-section label {
  display: block;
  font-weight: 700;
  color: #00b9ae;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.final-price-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #00b9ae;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.final-price-input:focus {
  outline: none;
  border-color: #037171;
  box-shadow: 0 0 0 3px rgba(0, 185, 174, 0.2);
}

.final-price-section small {
  display: block;
  color: #666;
  font-style: italic;
  margin-top: 0.5rem;
  text-align: center;
}

/* SuppliersPartsReparation Table */
.suppliers-parts-reparation-section {
  margin-top: 3rem;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 2px solid #00b9ae;
}

.suppliers-parts-reparation-section .section-header {
  background: linear-gradient(135deg, #037171, #03312e);
  color: white;
  padding: 1.8rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(3, 113, 113, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.suppliers-parts-reparation-section .section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  pointer-events: none;
}

.suppliers-parts-reparation-section .section-header h2 {
  color: white !important;
  font-size: 1.8rem;
  font-weight: 800;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.3px;
}

.suppliers-parts-reparation-section .header-title {
  flex: 1;
}

.suppliers-parts-reparation-section .header-subtitle {
  margin: 0.5rem 0 0 0;
  font-size: 1rem;
  opacity: 0.9;
  color: white;
  font-weight: 500;
}

.suppliers-parts-reparation-section .header-actions {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.suppliers-parts-reparation-section .header-actions .btn-modern {
  padding: 0.6rem 1.2rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.suppliers-parts-reparation-section .header-actions .btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.suppliers-parts-reparation-section .header-actions .btn-modern .btn-icon {
  font-size: 1rem;
}

.suppliers-parts-reparation-section .header-actions .btn-modern .btn-text {
  font-size: 0.85rem;
}

/* Direction handling for suppliers section */
.suppliers-parts-reparation-section .section-header-ltr {
  direction: ltr;
  text-align: left;
}

.suppliers-parts-reparation-section .section-header-rtl {
  direction: rtl;
  text-align: right;
}

/* Responsive design for suppliers section */
@media (max-width: 1200px) {
  .suppliers-parts-reparation-section .section-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .suppliers-parts-reparation-section .header-actions {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.6rem;
  }

  .suppliers-parts-reparation-section .header-actions .btn-modern {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  .suppliers-parts-reparation-section .section-header {
    padding: 1.5rem;
  }

  .suppliers-parts-reparation-section .section-header h2 {
    font-size: 1.4rem;
  }

  .suppliers-parts-reparation-section .header-subtitle {
    font-size: 0.85rem;
  }

  .suppliers-parts-reparation-section .header-actions {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }

  .suppliers-parts-reparation-section .header-actions .btn-modern {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
    width: 100%;
    justify-content: center;
  }
}

.supplier-row {
  cursor: pointer;
  transition: all 0.3s ease;
}

.supplier-row:hover {
  background: linear-gradient(135deg, #a9f9f4, #b9fff9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 185, 174, 0.15);
}

.credit-amount {
  color: #037171 !important;
  font-size: 1.2rem;
}

.suppliers-parts-reparation-section .data-table thead {
  background: linear-gradient(135deg, #037171, #03312e);
}

.suppliers-parts-reparation-section .data-table thead th {
  background: transparent;
  color: white !important;
  font-weight: 700;
  font-size: 1rem;
  padding: 1.2rem 1rem;
  text-align: center;
  border: none;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.recovery-btn {
  padding: 1.5rem 3rem;
  font-size: 1.3rem;
  font-weight: 700;
  border-radius: 16px;
  border: none;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.recovery-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.thermal-print-options {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #f39c12;
}

.thermal-print-options p {
  font-weight: 600;
  color: #666;
  margin: 0 0 1rem 0;
  text-align: center;
}

.print-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.print-actions .btn {
  padding: 1rem 2rem;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
}

.print-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Responsive Design for Recovery Modal */
@media (max-width: 768px) {
  .summary-grid {
    grid-template-columns: 1fr;
  }

  .scanner-frame {
    flex-direction: column;
    gap: 1rem;
  }

  .print-actions {
    flex-direction: column;
  }

  .client-recovery-modal {
    width: 98%;
    max-height: 95vh;
  }

  .client-recovery-container {
    padding: 1rem;
  }
}

/* Modern Repair Modal Design */
.modern-repair-modal {
  max-width: 1000px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Modern Nouveau Bon Pour Modal */
.nouveau-bon-modal {
  max-width: 1200px;
  width: 95%;
  max-height: 95vh;
  border-radius: 24px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
  background: white;
  overflow: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.nouveau-bon-modal::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Fixed Layout for EN/FR - No Responsive */
.nouveau-bon-modal.lang-en,
.nouveau-bon-modal.lang-fr {
  max-width: 1600px;
  width: 90%;
  max-height: 85vh;
  direction: ltr;
}

.nouveau-bon-modal.lang-en .nouveau-form,
.nouveau-bon-modal.lang-fr .nouveau-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
  direction: ltr;
}

.nouveau-bon-modal.lang-en .nouveau-form-actions,
.nouveau-bon-modal.lang-fr .nouveau-form-actions {
  grid-column: 1 / -1;
  direction: ltr;
}

.nouveau-bon-modal.lang-en .form-grid,
.nouveau-bon-modal.lang-fr .form-grid {
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.nouveau-bon-modal.lang-en .modern-radio-group,
.nouveau-bon-modal.lang-fr .modern-radio-group {
  display: flex;
  gap: 2rem;
  flex-direction: row;
}

/* Clean Direction for EN/FR */
.nouveau-bon-modal.lang-en *,
.nouveau-bon-modal.lang-fr * {
  direction: ltr;
  text-align: left;
}

.nouveau-bon-modal.lang-en .header-content,
.nouveau-bon-modal.lang-fr .header-content {
  text-align: left;
}

.nouveau-bon-modal.lang-en .modern-close,
.nouveau-bon-modal.lang-fr .modern-close {
  right: 1.5rem;
  left: auto;
}

.modern-overlay {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
}

.nouveau-header,
.modal-header.nouveau-header,
.modal-header-ltr.nouveau-header {
  background: linear-gradient(135deg, #498C8A, #3a7270) !important;
  color: white !important;
  padding: 2rem;
  border-bottom: none;
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
  width: 100% !important;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-icon {
  font-size: 3rem;
  opacity: 0.9;
  color: white !important;
}

.header-text h2,
.nouveau-header .header-text h2,
.reparation-header .header-text h2,
.recuperation-header .header-text h2,
.modal-header.nouveau-header .header-text h2,
.modal-header.reparation-header .header-text h2,
.modal-header.recuperation-header .header-text h2,
.modal-header-ltr.nouveau-header .header-text h2,
.modal-header-ltr.reparation-header .header-text h2,
.modal-header-ltr.recuperation-header .header-text h2 {
  margin: 0 !important;
  font-size: 2.2rem !important;
  font-weight: 800 !important;
  color: white !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.header-text p,
.nouveau-header .header-text p,
.reparation-header .header-text p,
.recuperation-header .header-text p,
.modal-header.nouveau-header .header-text p,
.modal-header.reparation-header .header-text p,
.modal-header.recuperation-header .header-text p,
.modal-header-ltr.nouveau-header .header-text p,
.modal-header-ltr.reparation-header .header-text p,
.modal-header-ltr.recuperation-header .header-text p {
  margin: 0.5rem 0 0 0 !important;
  opacity: 0.9 !important;
  font-size: 1.1rem !important;
  color: white !important;
}

.modern-close {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Nouveau Form Container */
.nouveau-form-container {
  padding: 0;
  background: #f8f9fa;
  max-height: calc(95vh - 120px);
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  position: relative; /* Ensure sticky positioning context */
}

.nouveau-form-container::-webkit-scrollbar {
  display: none; /* WebKit */
}

.nouveau-form {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Form Sections */
.form-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f3f4;
}

.section-icon {
  font-size: 1.8rem;
  color: #00b9ae;
}

.section-title h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 700;
  color: #333;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modern-label {
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
}

.modern-input, .modern-select, .modern-textarea {
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  font-family: inherit;
}

.modern-input:focus, .modern-select:focus, .modern-textarea:focus {
  outline: none;
  border-color: #00b9ae;
  box-shadow: 0 0 0 3px rgba(0, 185, 174, 0.1);
}

.readonly-input {
  background: #f8f9fa !important;
  color: #6c757d;
  cursor: not-allowed;
}

.barcode-input {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  letter-spacing: 1px;
}

/* Price Input with Currency */
.input-with-currency {
  position: relative;
  display: flex;
  align-items: center;
}

.price-input {
  padding-right: 4rem !important;
}

.currency {
  position: absolute;
  right: 1rem;
  color: #6c757d;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Payment Status Section */
.payment-status-section {
  margin-top: 1rem;
}

.modern-radio-group {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
}

.modern-radio {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: white;
  flex: 1;
}

.modern-radio:hover {
  border-color: #00b9ae;
  background: rgba(0, 185, 174, 0.05);
}

.modern-radio input[type="radio"] {
  display: none;
}

.radio-checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #dee2e6;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
}

.modern-radio input[type="radio"]:checked + .radio-checkmark {
  border-color: #00b9ae;
  background: #00b9ae;
}

.modern-radio input[type="radio"]:checked + .radio-checkmark::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

.radio-text {
  font-weight: 500;
  color: #495057;
}

.modern-radio input[type="radio"]:checked ~ .radio-text {
  color: #00b9ae;
  font-weight: 600;
}

/* Form Actions */
.nouveau-form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding: 2rem;
  background: white;
  border-top: 1px solid #e9ecef;
  margin: 0 -2rem -2rem -2rem;
  border-radius: 0 0 24px 24px;
}

.btn-modern {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 180px;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #3a7270, #2d5856);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(73, 140, 138, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
}

.btn-icon {
  font-size: 1.2rem;
}

.btn-text {
  font-weight: 600;
}

/* Fixed Layout for Arabic - Match EN/FR Size */
.nouveau-bon-modal.lang-ar {
  max-width: 1600px;
  width: 90%;
  max-height: 85vh;
  direction: rtl;
}

.nouveau-bon-modal.lang-ar .nouveau-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
  direction: rtl;
}

.nouveau-bon-modal.lang-ar .nouveau-form-actions {
  grid-column: 1 / -1;
  direction: rtl;
}

.nouveau-bon-modal.lang-ar .form-grid {
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.nouveau-bon-modal.lang-ar .modern-radio-group {
  display: flex;
  gap: 2rem;
  flex-direction: row;
}

/* Clean Direction for Arabic */
.nouveau-bon-modal.lang-ar * {
  direction: rtl;
  text-align: right;
}

.nouveau-bon-modal.lang-ar .header-content {
  text-align: right;
}

.nouveau-bon-modal.lang-ar .modern-close {
  left: 1.5rem;
  right: auto;
}

/* Responsive Design for All Languages - Keep Same Size */
@media (max-width: 768px) {
  /* Keep all languages landscape even on mobile */
  .nouveau-bon-modal.lang-ar,
  .nouveau-bon-modal.lang-en,
  .nouveau-bon-modal.lang-fr {
    width: 95%;
    max-width: 1600px;
    max-height: 85vh;
  }
}

/* Modern Réparation Terminée Modal */
.reparation-terminee-modal {
  max-width: 1400px;
  width: 95%;
  max-height: 95vh;
  border-radius: 24px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
  background: white;
  overflow: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.reparation-terminee-modal::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Fixed Layout for Arabic - Match EN/FR Size */
.reparation-terminee-modal.lang-ar {
  max-width: 1800px;
  width: 90%;
  max-height: 85vh;
  direction: rtl;
}

.reparation-terminee-modal.lang-ar * {
  direction: rtl;
  text-align: right;
}

.reparation-terminee-modal.lang-ar .outcome-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.reparation-terminee-modal.lang-ar .repairs-grid {
  grid-template-columns: repeat(3, 1fr);
}

.reparation-terminee-modal.lang-ar .modern-close {
  left: 1.5rem;
  right: auto;
}

/* Clean Layout for EN/FR */
.reparation-terminee-modal.lang-en,
.reparation-terminee-modal.lang-fr {
  max-width: 1800px;
  width: 90%;
  max-height: 85vh;
  direction: ltr;
}

.reparation-terminee-modal.lang-en *,
.reparation-terminee-modal.lang-fr * {
  direction: ltr;
  text-align: left;
}

.reparation-terminee-modal.lang-en .outcome-options,
.reparation-terminee-modal.lang-fr .outcome-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.reparation-terminee-modal.lang-en .repairs-grid,
.reparation-terminee-modal.lang-fr .repairs-grid {
  grid-template-columns: repeat(3, 1fr);
}

.reparation-terminee-modal.lang-en .modern-close,
.reparation-terminee-modal.lang-fr .modern-close {
  right: 1.5rem;
  left: auto;
}

.reparation-header,
.modal-header.reparation-header,
.modal-header-ltr.reparation-header {
  background: linear-gradient(135deg, #46ACC2, #3a8fa3) !important;
  color: white !important;
  padding: 2rem;
  border-bottom: none;
  position: sticky;
  top: 0;
  z-index: 100;
}

.reparation-container {
  padding: 0;
  background: #f8f9fa;
  max-height: calc(95vh - 120px);
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.reparation-container::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Step Sections */
.step-section {
  padding: 2rem;
  background: white;
  margin: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #f1f3f4;
  position: relative;
}

.step-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  flex-shrink: 0;
}

.step-info h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.step-info p {
  margin: 0.5rem 0 0 0;
  color: #6c757d;
  font-size: 1rem;
}

.btn-back {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-back:hover {
  background: #5a6268;
  transform: translateY(-50%) translateY(-2px);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #6c757d;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h4 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
}

/* Repairs Grid */
.repairs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.repair-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.repair-card:hover {
  border-color: #00b9ae;
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 185, 174, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.client-info h4 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
}

.device-name {
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

.repair-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #00b9ae;
}

.card-body {
  margin-bottom: 1rem;
}

.problem-desc {
  color: #495057;
  font-size: 0.95rem;
  line-height: 1.4;
  margin: 0 0 1rem 0;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.repair-date {
  color: #6c757d;
  font-size: 0.85rem;
}

.card-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid #f1f3f4;
  color: #00b9ae;
  font-weight: 600;
}

.action-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.repair-card:hover .action-arrow {
  transform: translateX(4px);
}

/* Selected Repair Summary */
.selected-repair-summary {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  background: linear-gradient(135deg, #00b9ae, #037171);
  color: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.summary-icon {
  font-size: 2.5rem;
  opacity: 0.9;
}

.summary-info h4 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
}

.summary-info p {
  margin: 0.25rem 0;
  opacity: 0.9;
}

.original-price {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  margin-top: 0.5rem;
  display: inline-block;
}

/* Outcome Options */
.outcome-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.outcome-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.success-card {
  border-color: rgba(40, 167, 69, 0.3);
}

.success-card:hover {
  border-color: #28a745;
  box-shadow: 0 8px 30px rgba(40, 167, 69, 0.15);
}

.failure-card {
  border-color: rgba(220, 53, 69, 0.3);
}

.failure-card:hover {
  border-color: #dc3545;
  box-shadow: 0 8px 30px rgba(220, 53, 69, 0.15);
}

.outcome-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid #f1f3f4;
}

.outcome-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.success-icon {
  color: #28a745;
}

.failure-icon {
  color: #dc3545;
}

.outcome-info h4 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
}

.outcome-info p {
  margin: 0.5rem 0 0 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.outcome-form {
  padding: 1.5rem;
}

.full-width {
  grid-column: 1 / -1;
}

.field-note {
  color: #6c757d;
  font-size: 0.85rem;
  margin-top: 0.5rem;
  display: block;
}

.btn-success {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.btn-success:hover {
  background: linear-gradient(135deg, #218838, #1e7e34);
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #c82333, #bd2130);
}

/* Scanner and Search Row Layout */
.scanner-search-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.scanner-search-row .barcode-scanner-section,
.scanner-search-row .search-filter-section,
.scanner-search-row .pickup-barcode-section,
.scanner-search-row .pickup-filter-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

/* Specific styling for Réparation Terminée scanner */
.reparation-terminee-modal .pickup-barcode-section {
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  color: white;
}

.reparation-terminee-modal .pickup-barcode-section .scanner-header h4,
.reparation-terminee-modal .pickup-barcode-section .scanner-header p {
  color: white;
}

.reparation-terminee-modal .pickup-filter-section {
  background: white;
  border: 2px solid #46ACC2;
}

/* Specific styling for Récupération Client scanner */
.recuperation-client-modal .pickup-barcode-section {
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
}

.recuperation-client-modal .pickup-barcode-section .scanner-header h4,
.recuperation-client-modal .pickup-barcode-section .scanner-header p {
  color: white;
}

.recuperation-client-modal .pickup-filter-section {
  background: white;
  border: 2px solid #42F2F7;
}

/* Fixed Table Layout for Réparation Terminée */
.reparation-table-fixed {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.table-wrapper-fixed {
  position: relative;
}

.table-header-fixed {
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-header-fixed th {
  padding: 1rem;
  font-weight: 600;
  text-align: left;
  border-bottom: none;
}

.table-body-scrollable {
  max-height: 280px; /* Approximately 4-5 rows (each row ~56px) */
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #46ACC2 #f8f9fa;
}

.table-body-scrollable::-webkit-scrollbar {
  width: 6px;
}

.table-body-scrollable::-webkit-scrollbar-track {
  background: #f8f9fa;
}

.table-body-scrollable::-webkit-scrollbar-thumb {
  background: #46ACC2;
  border-radius: 3px;
}

.table-header-hidden {
  visibility: hidden;
  height: 0;
}

.table-header-hidden th {
  padding: 0;
  border: none;
  height: 0;
}

.reparation-fixed-table {
  width: 100%;
  table-layout: fixed;
}

/* Clean column layout for Réparation Terminée (6 columns) */
.reparation-fixed-table th:nth-child(1),
.reparation-fixed-table td:nth-child(1) { width: 20%; } /* Client Name */
.reparation-fixed-table th:nth-child(2),
.reparation-fixed-table td:nth-child(2) { width: 18%; } /* Device Name */
.reparation-fixed-table th:nth-child(3),
.reparation-fixed-table td:nth-child(3) { width: 25%; } /* Problem */
.reparation-fixed-table th:nth-child(4),
.reparation-fixed-table td:nth-child(4) { width: 15%; } /* Repair Price */
.reparation-fixed-table th:nth-child(5),
.reparation-fixed-table td:nth-child(5) { width: 12%; } /* Deposit Date */
.reparation-fixed-table th:nth-child(6),
.reparation-fixed-table td:nth-child(6) { width: 10%; } /* Actions */

/* Récupération Client Fixed Table Layout */
.recuperation-fixed-table {
  width: 100%;
  table-layout: fixed;
}

/* Clean column layout for Récupération Client (7 columns) */
.recuperation-fixed-table th:nth-child(1),
.recuperation-fixed-table td:nth-child(1) { width: 18%; } /* Client Name */
.recuperation-fixed-table th:nth-child(2),
.recuperation-fixed-table td:nth-child(2) { width: 16%; } /* Device Name */
.recuperation-fixed-table th:nth-child(3),
.recuperation-fixed-table td:nth-child(3) { width: 16%; white-space: nowrap; } /* Status - Increased and no wrap */
.recuperation-fixed-table th:nth-child(4),
.recuperation-fixed-table td:nth-child(4) { width: 14%; } /* Parts Price */
.recuperation-fixed-table th:nth-child(5),
.recuperation-fixed-table td:nth-child(5) { width: 14%; } /* Interest Rate */
.recuperation-fixed-table th:nth-child(6),
.recuperation-fixed-table td:nth-child(6) { width: 14%; } /* Total Amount */
.recuperation-fixed-table th:nth-child(7),
.recuperation-fixed-table td:nth-child(7) { width: 8%; } /* Deposit Date - Reduced */

/* New Column Styles for Récupération Client */
.parts-price-cell,
.interest-rate-cell {
  text-align: center;
  font-weight: 600;
}

.parts-price {
  color: #46ACC2;
  font-weight: 700;
}

.interest-rate {
  color: #42F2F7;
  font-weight: 700;
}

/* Clean table cell styling */
.reparation-fixed-table td,
.recuperation-fixed-table td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid #e9ecef;
  height: 56px; /* Fixed row height for consistent scrolling */
}

.reparation-fixed-table th,
.recuperation-fixed-table th {
  padding: 1rem 0.75rem;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Clean cell content styling */
.client-info,
.device-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.client-name,
.device-name {
  font-weight: 600;
  color: #2c3e50;
}

.client-phone,
.device-type {
  font-size: 0.85rem;
  color: #6c757d;
}

.problem-desc {
  font-size: 0.9rem;
  line-height: 1.3;
  color: #495057;
}

.repair-price,
.total-amount {
  font-weight: 700;
  color: #28a745;
}

.deposit-date {
  font-size: 0.9rem;
  color: #6c757d;
}

/* Fixed Table Layout for Récupération Client */
.pickup-repairs-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.pickup-repairs-table-container .table-header-fixed {
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
}

.pickup-repairs-table-container .table-body-scrollable::-webkit-scrollbar-thumb {
  background: #42F2F7;
}

/* Fixed Table Layout for Repair Orders */
.repair-orders-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.repair-orders-table-container .table-header-fixed {
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
}

.repair-orders-table-container .table-header-fixed th {
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
}

.repair-orders-table-container .table-body-scrollable::-webkit-scrollbar-thumb {
  background: #498C8A;
}

/* Enhanced Repair Table Container - Responsive with Horizontal Scroll */
.repair-orders-table-container {
  width: 100% !important;
  overflow-x: auto !important;
  overflow-y: auto !important;
  max-height: 400px !important;
  border: 1px solid #e9ecef !important;
  border-radius: 8px !important;
  background: white !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.repair-orders-table-container .data-table {
  min-width: 1200px !important;
  width: 100% !important;
}

.repair-orders-table-container .table-body-scrollable {
  max-height: 350px !important;
  overflow-y: auto !important;
  overflow-x: visible !important;
}

/* Action buttons column width */
.repair-orders-table-container th:last-child,
.repair-orders-table-container td:last-child {
  width: 200px !important;
  min-width: 200px !important;
  position: sticky !important;
  right: 0 !important;
  background: white !important;
  border-left: 1px solid #e9ecef !important;
  z-index: 10 !important;
}

.repair-orders-fixed-table {
  width: 100%;
  table-layout: fixed;
}

/* Clean column layout for Repair Orders (8 columns) */
.repair-orders-fixed-table th:nth-child(1),
.repair-orders-fixed-table td:nth-child(1) { width: 10%; } /* QR Code */
.repair-orders-fixed-table th:nth-child(2),
.repair-orders-fixed-table td:nth-child(2) { width: 15%; } /* Client Name */
.repair-orders-fixed-table th:nth-child(3),
.repair-orders-fixed-table td:nth-child(3) { width: 15%; } /* Device Name */
.repair-orders-fixed-table th:nth-child(4),
.repair-orders-fixed-table td:nth-child(4) { width: 20%; } /* Problem */
.repair-orders-fixed-table th:nth-child(5),
.repair-orders-fixed-table td:nth-child(5) { width: 12%; } /* Parts Price */
.repair-orders-fixed-table th:nth-child(6),
.repair-orders-fixed-table td:nth-child(6) { width: 12%; } /* Total Price */
.repair-orders-fixed-table th:nth-child(7),
.repair-orders-fixed-table td:nth-child(7) { width: 10%; } /* Status */
.repair-orders-fixed-table th:nth-child(8),
.repair-orders-fixed-table td:nth-child(8) { width: 6%; } /* Actions */

/* Fixed Table Layout for Suppliers */
.suppliers-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.suppliers-table-container .table-header-fixed {
  background: linear-gradient(135deg, #00b9ae, #008a82);
  color: white;
}

.suppliers-table-container .table-body-scrollable::-webkit-scrollbar-thumb {
  background: #00b9ae;
}

.suppliers-fixed-table {
  width: 100%;
  table-layout: fixed;
}

/* Clean column layout for Suppliers (4 columns) */
.suppliers-fixed-table th:nth-child(1),
.suppliers-fixed-table td:nth-child(1) { width: 30%; } /* Supplier Name */
.suppliers-fixed-table th:nth-child(2),
.suppliers-fixed-table td:nth-child(2) { width: 25%; } /* Total Credit */
.suppliers-fixed-table th:nth-child(3),
.suppliers-fixed-table td:nth-child(3) { width: 25%; } /* Transactions */
.suppliers-fixed-table th:nth-child(4),
.suppliers-fixed-table td:nth-child(4) { width: 20%; } /* Actions */

/* Supplier table action buttons styling */
.suppliers-fixed-table .action-buttons-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
  justify-content: center;
  align-items: center;
}

/* Supplier Table Action Button Colors - Match Header */
.suppliers-fixed-table .action-buttons-group .btn-primary {
  background: #00b9ae !important;
  color: white !important;
  border: 1px solid #00b9ae !important;
}

.suppliers-fixed-table .action-buttons-group .btn-primary:hover {
  background: #008a82 !important;
  border-color: #008a82 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 185, 174, 0.3);
}

.suppliers-fixed-table .action-buttons-group .btn-danger {
  background: #dc3545 !important;
  color: white !important;
  border: 1px solid #dc3545 !important;
}

.suppliers-fixed-table .action-buttons-group .btn-danger:hover {
  background: #c82333 !important;
  border-color: #c82333 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.suppliers-fixed-table .action-buttons-group .btn {
  padding: 0.4rem 0.6rem;
  font-size: 0.9rem;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.suppliers-fixed-table .action-buttons-group .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Supplier info styling */
.supplier-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.supplier-icon {
  font-size: 1.1rem;
}

.supplier-name {
  font-weight: 600;
  color: #037171;
}

.credit-amount {
  font-weight: 600;
  font-size: 1.1rem;
  color: #037171;
}

.transactions-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.2rem;
}

.transaction-count {
  font-weight: 600;
  font-size: 1.1rem;
  color: #037171;
}

.transaction-label {
  font-size: 0.8rem;
  color: #666;
}

/* Responsive Design for All Languages */
@media (max-width: 1200px) {
  .scanner-search-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Make tables responsive on tablets */
  .reparation-fixed-table,
  .recuperation-fixed-table,
  .suppliers-fixed-table {
    font-size: 0.9rem;
  }

  .reparation-fixed-table th,
  .reparation-fixed-table td,
  .recuperation-fixed-table th,
  .recuperation-fixed-table td,
  .suppliers-fixed-table th,
  .suppliers-fixed-table td {
    padding: 0.75rem 0.5rem;
  }

  /* Supplier table responsive adjustments */
  .suppliers-fixed-table .action-buttons-group {
    gap: 0.2rem;
  }

  .suppliers-fixed-table .action-buttons-group .btn {
    padding: 0.3rem 0.5rem;
    font-size: 0.8rem;
    min-width: 28px;
    height: 28px;
  }

  .supplier-info {
    gap: 0.3rem;
  }

  .supplier-name {
    font-size: 0.9rem;
  }

  .credit-amount {
    font-size: 1rem;
  }

  .transaction-count {
    font-size: 1rem;
  }

  .reparation-terminee-modal.lang-ar .outcome-options {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  /* Mobile responsiveness for all languages */
  .reparation-terminee-modal,
  .recuperation-client-modal {
    width: 98%;
    max-height: 98vh;
    margin: 1vh auto;
  }

  .reparation-terminee-modal .reparation-header,
  .reparation-terminee-modal .modal-header.reparation-header,
  .reparation-terminee-modal .modal-header-ltr.reparation-header,
  .recuperation-client-modal .recuperation-header,
  .recuperation-client-modal .modal-header.recuperation-header,
  .recuperation-client-modal .modal-header-ltr.recuperation-header {
    padding: 1.5rem !important;
  }

  .reparation-terminee-modal .reparation-header,
  .reparation-terminee-modal .modal-header.reparation-header,
  .reparation-terminee-modal .modal-header-ltr.reparation-header {
    background: linear-gradient(135deg, #46ACC2, #3a8fa3) !important;
    color: white !important;
  }

  .recuperation-client-modal .recuperation-header,
  .recuperation-client-modal .modal-header.recuperation-header,
  .recuperation-client-modal .modal-header-ltr.recuperation-header {
    background: linear-gradient(135deg, #42F2F7, #36c7cc) !important;
    color: white !important;
  }

  /* Mobile table responsiveness */
  .table-body-scrollable {
    max-height: 220px; /* Approximately 4 rows on mobile */
  }

  .reparation-fixed-table,
  .recuperation-fixed-table {
    font-size: 0.8rem;
  }

  .reparation-fixed-table th,
  .reparation-fixed-table td,
  .recuperation-fixed-table th,
  .recuperation-fixed-table td {
    padding: 0.5rem 0.25rem;
    min-width: 80px;
  }

  /* Hide less important columns on mobile and adjust remaining widths */
  .reparation-fixed-table th:nth-child(3),
  .reparation-fixed-table td:nth-child(3) { display: none; } /* Hide Problem column */

  .recuperation-fixed-table th:nth-child(4),
  .recuperation-fixed-table td:nth-child(4),
  .recuperation-fixed-table th:nth-child(5),
  .recuperation-fixed-table td:nth-child(5) { display: none; } /* Hide Parts Price and Interest Rate */

  /* Adjust remaining column widths for mobile */
  .reparation-fixed-table th:nth-child(1),
  .reparation-fixed-table td:nth-child(1) { width: 30%; } /* Client Name */
  .reparation-fixed-table th:nth-child(2),
  .reparation-fixed-table td:nth-child(2) { width: 25%; } /* Device Name */
  .reparation-fixed-table th:nth-child(4),
  .reparation-fixed-table td:nth-child(4) { width: 20%; } /* Repair Price */
  .reparation-fixed-table th:nth-child(5),
  .reparation-fixed-table td:nth-child(5) { width: 15%; } /* Deposit Date */
  .reparation-fixed-table th:nth-child(6),
  .reparation-fixed-table td:nth-child(6) { width: 10%; } /* Actions */

  .recuperation-fixed-table th:nth-child(1),
  .recuperation-fixed-table td:nth-child(1) { width: 25%; } /* Client Name */
  .recuperation-fixed-table th:nth-child(2),
  .recuperation-fixed-table td:nth-child(2) { width: 20%; } /* Device Name */
  .recuperation-fixed-table th:nth-child(3),
  .recuperation-fixed-table td:nth-child(3) { width: 15%; } /* Status */
  .recuperation-fixed-table th:nth-child(6),
  .recuperation-fixed-table td:nth-child(6) { width: 20%; } /* Total Amount */
  .recuperation-fixed-table th:nth-child(7),
  .recuperation-fixed-table td:nth-child(7) { width: 12%; } /* Deposit Date */
  .recuperation-fixed-table th:nth-child(8),
  .recuperation-fixed-table td:nth-child(8) { width: 8%; } /* Actions */

  /* Keep all languages landscape even on mobile */
  .reparation-terminee-modal.lang-ar,
  .reparation-terminee-modal.lang-en,
  .reparation-terminee-modal.lang-fr {
    width: 95%;
    max-width: 1800px;
    max-height: 85vh;
  }
}

/* Modern Récupération Client Modal */
.recuperation-client-modal {
  max-width: 1400px;
  width: 95%;
  max-height: 95vh;
  border-radius: 24px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
  background: white;
  overflow: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.recuperation-client-modal::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Fixed Layout for Arabic - Match EN/FR Size */
.recuperation-client-modal.lang-ar {
  max-width: 1800px;
  width: 90%;
  max-height: 85vh;
  direction: rtl;
}

.recuperation-client-modal.lang-ar * {
  direction: rtl;
  text-align: right;
}

.recuperation-client-modal.lang-ar .search-methods {
  grid-template-columns: repeat(3, 1fr);
}

.recuperation-client-modal.lang-ar .detail-grid {
  grid-template-columns: repeat(3, 1fr);
}

/* Landscape Layout for EN/FR */
.recuperation-client-modal.lang-en,
.recuperation-client-modal.lang-fr {
  max-width: 1800px;
  width: 90%;
  max-height: 85vh;
  direction: ltr;
}

.recuperation-client-modal.lang-en *,
.recuperation-client-modal.lang-fr * {
  direction: ltr;
  text-align: left;
}

.recuperation-client-modal.lang-en .search-methods,
.recuperation-client-modal.lang-fr .search-methods {
  grid-template-columns: repeat(3, 1fr);
}

.recuperation-client-modal.lang-en .detail-grid,
.recuperation-client-modal.lang-fr .detail-grid {
  grid-template-columns: repeat(3, 1fr);
}

.recuperation-header,
.modal-header.recuperation-header,
.modal-header-ltr.recuperation-header {
  background: linear-gradient(135deg, #42F2F7, #36c7cc) !important;
  color: white !important;
  padding: 2rem;
  border-bottom: none;
  position: sticky;
  top: 0;
  z-index: 100;
}

.recuperation-container {
  padding: 0;
  background: #f8f9fa;
  max-height: calc(95vh - 120px);
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.recuperation-container::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Search Methods */
.search-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.search-method {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.search-method:hover {
  border-color: #17a2b8;
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(23, 162, 184, 0.15);
}

.method-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.method-icon {
  font-size: 2rem;
  color: #17a2b8;
}

.method-info h4 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
}

.method-info p {
  margin: 0.25rem 0 0 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.method-btn {
  width: 100%;
  justify-content: center;
  padding: 1.25rem;
}

.manual-search-form {
  margin-top: 1rem;
}

.search-input {
  width: 100%;
  font-size: 1.1rem;
  padding: 1.25rem;
}

.list-select {
  width: 100%;
  font-size: 1rem;
  padding: 1.25rem;
}

/* Repair Summary Card */
.repair-summary-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  margin-bottom: 2rem;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
  position: relative;
}

.client-avatar {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-shrink: 0;
}

.client-details h4 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.client-details p {
  margin: 0.25rem 0;
  opacity: 0.9;
  font-size: 1rem;
}

.repair-id {
  position: absolute;
  top: 1rem;
  right: 2rem;
  text-align: right;
}

.id-label {
  display: block;
  font-size: 0.8rem;
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

.id-value {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  font-size: 0.9rem;
}

.summary-details {
  padding: 2rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.detail-icon {
  font-size: 1.5rem;
  color: #17a2b8;
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
}

.detail-value {
  font-size: 1rem;
  color: #333;
  font-weight: 600;
  margin-top: 0.25rem;
}

/* Pricing Summary */
.pricing-summary {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  padding: 1.5rem;
}

.price-breakdown {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
}

.price-item:not(:last-child) {
  border-bottom: 1px solid #dee2e6;
}

.price-label {
  font-weight: 500;
  color: #495057;
}

.price-value {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

.total-price-item {
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 0.5rem;
}

.total-price-item .price-label,
.total-price-item .price-value {
  color: white;
}

.total-amount {
  font-size: 1.3rem !important;
  font-weight: 700 !important;
}

/* Completion Section */
.completion-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.final-price-adjustment {
  margin-bottom: 2rem;
}

.completion-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.completion-btn {
  flex: 2;
  padding: 1.5rem;
  font-size: 1.1rem;
  font-weight: 700;
}

.print-options {
  flex: 1;
}

.print-btn {
  width: 100%;
  padding: 1.5rem;
}

/* Responsive Design for All Languages - Keep Same Size */
@media (max-width: 768px) {
  /* Keep all languages landscape even on mobile */
  .recuperation-client-modal.lang-ar,
  .recuperation-client-modal.lang-en,
  .recuperation-client-modal.lang-fr {
    width: 95%;
    max-width: 1800px;
    max-height: 85vh;
  }
}

/* Enhanced Supplier Table Styles */
.modern-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #00b9ae, #037171);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 185, 174, 0.3);
}

.header-title h2 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 800;
  color: white !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
  margin: 0.5rem 0 0 0;
  opacity: 0.9;
  font-size: 1rem;
  color: white;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.modern-table-container {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.modern-suppliers-table {
  width: 100%;
  border-collapse: collapse;
}

.modern-suppliers-table thead {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.modern-suppliers-table thead th {
  padding: 1.5rem;
  font-weight: 700;
  color: #333;
  text-align: left;
  border-bottom: 2px solid #dee2e6;
  font-size: 1rem;
}

.table-rtl .modern-suppliers-table thead th {
  text-align: right;
}

.modern-supplier-row {
  transition: all 0.3s ease;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
}

.modern-supplier-row:hover {
  background: linear-gradient(135deg, rgba(0, 185, 174, 0.05), rgba(3, 113, 113, 0.05));
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 185, 174, 0.1);
}

.modern-supplier-row td {
  padding: 1.5rem;
  vertical-align: middle;
}

.supplier-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.supplier-icon {
  font-size: 1.5rem;
  color: #00b9ae;
}

.supplier-name {
  font-size: 1.1rem;
  color: #333;
  font-weight: 600;
}

.credit-amount {
  font-size: 1.2rem;
  font-weight: 700;
  color: #00b9ae;
  background: rgba(0, 185, 174, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  display: inline-block;
}

.transactions-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.table-rtl .transactions-info {
  align-items: flex-end;
}

.transaction-count {
  font-size: 1.3rem;
  font-weight: 700;
  color: #333;
}

.transaction-label {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.modern-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-start;
}

.table-rtl .modern-actions {
  justify-content: flex-end;
}

.btn-action {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.btn-view {
  background: linear-gradient(135deg, #17a2b8, #20c997);
  color: white;
}

.btn-view:hover {
  background: linear-gradient(135deg, #138496, #1e7e34);
  transform: scale(1.1);
}

.btn-print {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
}

.btn-print:hover {
  background: linear-gradient(135deg, #5a6268, #343a40);
  transform: scale(1.1);
}

.empty-state-inline {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 3rem;
  color: #6c757d;
}

.empty-icon {
  font-size: 2.5rem;
  opacity: 0.5;
}

.empty-text {
  font-size: 1.1rem;
  font-weight: 500;
}

/* Responsive Design for Supplier Table */
@media (max-width: 1200px) {
  .modern-section-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .btn-modern {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .modern-section-header {
    padding: 1.5rem;
  }

  .header-title h2 {
    font-size: 1.5rem;
  }

  .modern-suppliers-table thead th,
  .modern-supplier-row td {
    padding: 1rem;
  }

  .supplier-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .table-rtl .supplier-info {
    align-items: flex-end;
  }

  .modern-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .btn-action {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
}

/* Modern Big Action Buttons Row */
.modern-repair-actions-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 2rem;
  margin: 3rem 0;
  padding: 0 2rem;
}

.actions-ltr {
  direction: ltr;
}

.actions-rtl {
  direction: rtl;
}

.big-action-btn {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 3rem 2rem;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  min-height: 150px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.actions-rtl .big-action-btn {
  text-align: right;
}

.big-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  pointer-events: none;
}

.big-action-btn:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.big-btn-icon {
  font-size: 4rem;
  flex-shrink: 0;
  opacity: 0.9;
}

.big-btn-content {
  flex: 1;
}

.big-btn-content h2 {
  margin: 0 0 1rem 0;
  font-size: 1.8rem;
  font-weight: 800;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.big-btn-content p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
  color: white;
  line-height: 1.4;
}

/* Individual Button Colors */
.nouveau-bon-btn {
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
}

.nouveau-bon-btn:hover {
  background: linear-gradient(135deg, #3a7270, #2d5856);
}

.reparation-terminee-btn {
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  color: white;
}

.reparation-terminee-btn:hover {
  background: linear-gradient(135deg, #3a8fa3, #2e7282);
}

.waiting-client-btn {
  background: linear-gradient(135deg, #42F2F7, #36c7cc);
  color: white;
}

.waiting-client-btn:hover {
  background: linear-gradient(135deg, #36c7cc, #2aa0a5);
}

/* Responsive Design for Big Action Buttons */
@media (max-width: 1200px) {
  .modern-repair-actions-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .big-action-btn {
    padding: 2rem;
    min-height: 120px;
  }

  .big-btn-icon {
    font-size: 3rem;
  }

  .big-btn-content h2 {
    font-size: 1.5rem;
  }

  .big-btn-content p {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .modern-repair-actions-row {
    padding: 0 1rem;
    gap: 1rem;
  }

  .big-action-btn {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
    gap: 1rem;
    min-height: 100px;
  }

  .actions-rtl .big-action-btn {
    text-align: center;
  }

  .big-btn-icon {
    font-size: 2.5rem;
  }

  .big-btn-content h2 {
    font-size: 1.3rem;
  }

  .big-btn-content p {
    font-size: 0.9rem;
  }
}

/* Modern Repair Orders Header */
.repair-orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 2rem 0 1.5rem 0;
  padding: 1.8rem;
  background: linear-gradient(135deg, #00b9ae, #037171);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 185, 174, 0.3);
  position: relative;
  overflow: hidden;
}

.repair-orders-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  pointer-events: none;
}

.header-ltr {
  direction: ltr;
  text-align: left;
}

.header-rtl {
  direction: rtl;
  text-align: right;
}

.orders-title-section {
  flex: 1;
}

.orders-main-title {
  margin: 0 0 1rem 0;
  font-size: 1.8rem;
  font-weight: 800;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.3px;
}

.orders-subtitle {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
  color: white;
  font-weight: 500;
}

.header-actions-section {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.header-actions-section .btn-modern {
  padding: 0.6rem 1.2rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-actions-section .btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.header-actions-section .btn-modern .btn-icon {
  font-size: 1rem;
}

.header-actions-section .btn-modern .btn-text {
  font-size: 0.85rem;
}

/* Modern Table Filters */
.modern-table-filters {
  margin: 2rem 0;
  padding: 0 2rem;
}

.filters-ltr {
  direction: ltr;
}

.filters-rtl {
  direction: rtl;
}

.filter-section {
  display: flex;
  gap: 2rem;
  align-items: center;
  background: white;
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.search-filter {
  flex: 2;
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 1rem;
  font-size: 1.2rem;
  color: #6c757d;
  z-index: 1;
}

.filters-rtl .search-icon {
  left: auto;
  right: 1rem;
}

.modern-search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
  position: relative;
  z-index: 2; /* Ensure input is above icons */
}

.filters-rtl .modern-search-input {
  padding: 1rem 3rem 1rem 1rem;
  text-align: right;
}

.modern-search-input:focus {
  outline: none;
  border-color: #00b9ae;
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 185, 174, 0.1);
  z-index: 3; /* Higher z-index when focused */
}

.status-filter {
  flex: 1;
}

.modern-status-select {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  background: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.modern-status-select:focus {
  outline: none;
  border-color: #00b9ae;
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 185, 174, 0.1);
}

/* Responsive Design for Header and Filters */
@media (max-width: 1200px) {
  .repair-orders-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .orders-main-title {
    font-size: 1.6rem;
  }

  .orders-subtitle {
    font-size: 0.95rem;
  }

  .header-actions-section {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.6rem;
  }

  .header-actions-section .btn-modern {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }

  .filter-section {
    flex-direction: column;
    gap: 1.5rem;
  }

  .search-filter,
  .status-filter {
    flex: none;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .repair-orders-header {
    margin: 1.5rem 0 1rem 0;
    padding: 1.5rem;
  }

  .orders-main-title {
    font-size: 1.4rem;
  }

  .orders-subtitle {
    font-size: 0.85rem;
  }

  .header-actions-section {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }

  .header-actions-section .btn-modern {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
    width: 100%;
    justify-content: center;
  }

  .modern-table-filters {
    padding: 0 1rem;
  }

  .filter-section {
    padding: 1rem;
  }

  .modern-search-input,
  .modern-status-select {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .filters-rtl .modern-search-input {
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  }
}

.modern-repair-modal .modal-header {
  background: linear-gradient(135deg, #00b9ae, #037171);
  color: white;
  padding: 2.5rem;
  border-radius: 20px 20px 0 0;
  position: relative;
  overflow: hidden;
}

.modern-repair-modal .modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.modern-repair-modal .modal-header h2 {
  color: white !important;
  font-size: 2.2rem;
  font-weight: 800;
  margin: 0 0 0.5rem 0;
  position: relative;
  z-index: 1;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.modern-repair-modal .modal-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin: 0;
  position: relative;
  z-index: 1;
}

.modern-repair-modal .modal-close {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modern-repair-modal .modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.modern-repair-modal .repair-form-container {
  padding: 3rem;
  background: linear-gradient(135deg, #a9f9f4, #b9fff9);
}

.modern-repair-modal .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.modern-repair-modal .form-group {
  margin-bottom: 0;
}

.modern-repair-modal .form-group.full-width {
  grid-column: 1 / -1;
}

.modern-repair-modal .form-group label {
  display: block;
  margin-bottom: 0.8rem;
  font-weight: 700;
  font-size: 1.1rem;
  color: #333;
  font-family: 'Cairo', sans-serif;
}

.modern-repair-modal .form-group input,
.modern-repair-modal .form-group select,
.modern-repair-modal .form-group textarea {
  width: 100%;
  padding: 1.2rem;
  border: 3px solid #00b9ae;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  background: white;
  transition: all 0.3s ease;
  font-family: 'Cairo', sans-serif;
}

.modern-repair-modal .form-group input:focus,
.modern-repair-modal .form-group select:focus,
.modern-repair-modal .form-group textarea:focus {
  outline: none;
  border-color: #037171;
  box-shadow: 0 0 0 4px rgba(0, 185, 174, 0.2);
  transform: translateY(-2px);
}

.modern-repair-modal .form-group input::placeholder,
.modern-repair-modal .form-group textarea::placeholder {
  color: #999;
  font-weight: 500;
}

.modern-repair-modal .radio-group {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.modern-repair-modal .radio-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: white;
  border: 3px solid #00b9ae;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.modern-repair-modal .radio-option:hover {
  background: #a9f9f4;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 185, 174, 0.3);
}

.modern-repair-modal .radio-option input[type="radio"] {
  width: auto;
  margin: 0;
  transform: scale(1.2);
}

.modern-repair-modal .form-actions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 3px solid #00b9ae;
}

.modern-repair-modal .form-actions .btn {
  padding: 1.5rem 3rem;
  font-size: 1.2rem;
  font-weight: 700;
  border-radius: 16px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.modern-repair-modal .form-actions .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.modern-repair-modal .form-actions .btn-success {
  background: linear-gradient(135deg, #00b9ae, #037171);
  color: white;
}

.modern-repair-modal .form-actions .btn-secondary {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
}

/* Responsive Design for Modern Repair Modal */
@media (max-width: 768px) {
  .modern-repair-modal .form-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .modern-repair-modal .radio-group {
    flex-direction: column;
    gap: 1rem;
  }

  .modern-repair-modal .form-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .modern-repair-modal {
    width: 98%;
    max-height: 95vh;
  }

  .modern-repair-modal .repair-form-container {
    padding: 1.5rem;
  }

  .modern-repair-modal .modal-header {
    padding: 2rem;
  }
}

/* Status Badges for Repairs */
.status-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-inProcess {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: white;
}

.status-waitingForClient {
  background: linear-gradient(135deg, #17a2b8, #20c997);
  color: white;
}

.status-done {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.status-notSuccess {
  background: linear-gradient(135deg, #dc3545, #e83e8c);
  color: white;
}

/* Repair Modal Styles */
.repair-modal {
  max-width: 900px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
}

.repair-form-container {
  padding: 2rem;
}

.repair-form-container .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.repair-form-container .form-group.full-width {
  grid-column: 1 / -1;
}

.repair-form-container .form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Cairo', sans-serif;
}

.repair-form-container .form-group input,
.repair-form-container .form-group select,
.repair-form-container .form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.repair-form-container .form-group input:focus,
.repair-form-container .form-group select:focus,
.repair-form-container .form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.repair-form-container .readonly-field {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.repair-form-container .radio-group {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.repair-form-container .radio-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 500;
}

.repair-form-container .radio-option input[type="radio"] {
  width: auto;
  margin: 0;
}

.repair-form-container .form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 2px solid var(--border-color);
}

/* Arabic RTL Support for Repair Form */
.lang-ar .repair-form-container .form-group label {
  text-align: right;
  font-family: 'Cairo', sans-serif;
  font-weight: 700;
}

.lang-ar .repair-form-container .radio-group {
  flex-direction: row-reverse;
}

.lang-ar .repair-form-container .radio-option {
  flex-direction: row-reverse;
}

.lang-ar .repair-form-container .form-actions {
  justify-content: flex-start;
}

/* Responsive Design for Repair Page */
@media (max-width: 768px) {
  .repair-action-buttons {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .repair-action-btn {
    padding: 1.5rem;
    min-height: 100px;
  }

  .action-btn-icon {
    font-size: 2.5rem;
  }

  .action-btn-content h3 {
    font-size: 1.2rem;
  }

  .action-btn-content p {
    font-size: 0.9rem;
  }

  .repair-modal {
    width: 98%;
    max-height: 95vh;
  }

  .repair-form-container {
    padding: 1rem;
  }

  .repair-form-container .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .repair-form-container .radio-group {
    flex-direction: column;
    gap: 1rem;
  }

  .repair-form-container .form-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Repair Status Modal Styles */
.repair-status-modal {
  max-width: 800px;
  width: 95%;
}

.repair-status-container {
  padding: 2rem;
}

.status-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.status-option {
  padding: 2rem;
  border-radius: 16px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.success-option {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
  border-color: rgba(40, 167, 69, 0.3);
}

.failure-option {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(232, 62, 140, 0.1));
  border-color: rgba(220, 53, 69, 0.3);
}

.status-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.status-option h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 700;
  font-family: 'Cairo', sans-serif;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  width: 100%;
  margin-top: 1rem;
}

/* Client Pickup Modal Styles */
.client-pickup-modal {
  max-width: 700px;
  width: 95%;
}

.client-pickup-container {
  padding: 2rem;
}

.qr-scanner-section {
  text-align: center;
  padding: 2rem;
  background: var(--card-bg);
  border-radius: 16px;
  margin-bottom: 2rem;
}

.scanner-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.qr-scanner-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.qr-scanner-section p {
  margin: 0 0 2rem 0;
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.scanner-btn {
  padding: 1rem 2rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.repair-summary {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 2rem;
  border: 2px solid var(--border-color);
}

.repair-summary h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 1rem;
}

.summary-details {
  margin-bottom: 2rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .label {
  font-weight: 600;
  color: var(--text-secondary);
}

.detail-row .value {
  font-weight: 700;
  color: var(--text-primary);
}

.total-row {
  background: rgba(0, 123, 255, 0.1);
  padding: 1rem;
  border-radius: 8px;
  border: 2px solid rgba(0, 123, 255, 0.2);
  margin-top: 1rem;
}

.total-row .label,
.total-row .value {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.pickup-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Modern Admin Confirmation Modal */
.admin-confirmation-modal {
  max-width: 500px;
  width: 90%;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.admin-confirmation-modal .modal-header,
.admin-confirmation-modal .modal-header-ltr {
  background: linear-gradient(135deg, #dc3545, #c82333) !important;
  color: white !important;
  padding: 2rem;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
  position: relative;
  overflow: hidden;
  border-bottom: none;
}

.admin-confirmation-modal .modal-header h2,
.admin-confirmation-modal .modal-header-ltr h2 {
  color: white !important;
  font-size: 1.8rem !important;
  font-weight: 800 !important;
  margin: 0 !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.admin-confirmation-container {
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  text-align: center;
}

.admin-confirmation-icon {
  font-size: 4rem;
  color: #dc3545;
  margin-bottom: 1rem;
}

.admin-confirmation-message {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.admin-passcode-input {
  width: 100%;
  padding: 1.2rem;
  border: 3px solid #dc3545;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
  background: white;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
  font-family: 'Cairo', sans-serif;
}

.admin-passcode-input:focus {
  outline: none;
  border-color: #c82333;
  box-shadow: 0 0 0 4px rgba(220, 53, 69, 0.2);
  transform: translateY(-2px);
}

.admin-confirmation-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.admin-confirmation-actions .btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.admin-confirmation-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.admin-confirmation-actions .btn-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.admin-confirmation-actions .btn-secondary {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
}

/* QR Scanner Modal Styles */
.qr-scanner-modal {
  max-width: 500px;
  width: 95%;
}

.qr-scanner-container {
  padding: 2rem;
  text-align: center;
}

.scanner-area {
  margin-bottom: 2rem;
}

.scanner-frame {
  position: relative;
  width: 250px;
  height: 250px;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(108, 117, 125, 0.1));
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scanner-corners {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.corner {
  position: absolute;
  width: 30px;
  height: 30px;
  border: 3px solid var(--primary-color);
}

.corner.top-left {
  top: 10px;
  left: 10px;
  border-right: none;
  border-bottom: none;
}

.corner.top-right {
  top: 10px;
  right: 10px;
  border-left: none;
  border-bottom: none;
}

.corner.bottom-left {
  bottom: 10px;
  left: 10px;
  border-right: none;
  border-top: none;
}

.corner.bottom-right {
  bottom: 10px;
  right: 10px;
  border-left: none;
  border-top: none;
}

.scanner-line {
  width: 80%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  animation: scannerMove 2s ease-in-out infinite;
}

@keyframes scannerMove {
  0%, 100% { transform: translateY(-50px); opacity: 0; }
  50% { transform: translateY(0); opacity: 1; }
}

.manual-input-section {
  background: var(--card-bg);
  padding: 1.5rem;
  border-radius: 12px;
  border: 2px solid var(--border-color);
}

.manual-input-section h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.manual-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.manual-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Responsive Design for Status Modals */
@media (max-width: 768px) {
  .status-options {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .status-option {
    padding: 1.5rem;
  }

  .pickup-actions {
    flex-direction: column;
  }

  .scanner-frame {
    width: 200px;
    height: 200px;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Repair Info Modal Styles */
.repair-info-modal {
  max-width: 900px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
}

.repair-info-modal .modal-header,
.repair-info-modal .modal-header-ltr {
  background: linear-gradient(135deg, #00b9ae, #037171) !important;
  color: white !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 8px 32px rgba(0, 185, 174, 0.3);
  position: relative;
  overflow: hidden;
  border-bottom: none;
}

.repair-info-modal .modal-header h2,
.repair-info-modal .modal-header-ltr h2 {
  color: white !important;
  font-size: 2rem !important;
  font-weight: 800 !important;
  margin: 0 !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.modal-header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.repair-info-container {
  padding: 2rem;
  background: linear-gradient(135deg, #a9f9f4, #b9fff9);
  min-height: 400px;
}

.info-section {
  margin-bottom: 2rem;
  background: var(--card-bg);
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid var(--border-color);
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 0.75rem;
  font-family: 'Cairo', sans-serif;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(0, 123, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 123, 255, 0.1);
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item.total-item {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
  border-color: rgba(40, 167, 69, 0.3);
  border-width: 2px;
}

.info-label {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-weight: 700;
  color: var(--text-primary);
  font-size: 1.1rem;
  word-break: break-word;
}

.info-value.price {
  color: var(--success-color);
  font-size: 1.2rem;
}

.info-value.total-price {
  color: var(--success-color);
  font-size: 1.4rem;
  font-weight: 800;
}

.info-value.barcode {
  font-family: 'Courier New', monospace;
  background: var(--bg-secondary);
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 1rem;
}

.info-value.failure-text {
  color: var(--danger-color);
  font-style: italic;
}

/* Status badges in info modal */
.info-value.status-badge {
  display: inline-block;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Payment status colors */
.info-value.status-paid {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.info-value.status-partiallyPaid {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: white;
}

.info-value.status-unpaid {
  background: linear-gradient(135deg, #dc3545, #e83e8c);
  color: white;
}

/* Arabic RTL Support for Info Modal */
.lang-ar .info-section h3 {
  text-align: right;
  font-family: 'Cairo', sans-serif;
  font-weight: 700;
}

.lang-ar .info-item {
  text-align: right;
}

.lang-ar .info-label {
  font-family: 'Cairo', sans-serif;
  font-weight: 700;
}

.lang-ar .info-value {
  font-family: 'Cairo', sans-serif;
  font-weight: 700;
}

/* Responsive Design for Info Modal */
@media (max-width: 768px) {
  .repair-info-modal {
    width: 98%;
    max-height: 95vh;
  }

  .repair-info-container {
    padding: 1rem;
  }

  .info-section {
    padding: 1rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .info-item {
    padding: 0.75rem;
  }

  .modal-header-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  /* QR Display Modal Responsive */
  .qr-display-modal {
    width: 98%;
    max-height: 95vh;
  }

  .qr-display-modal .modal-header,
  .qr-display-modal .modal-header-ltr {
    padding: 1.5rem !important;
  }

  .qr-display-modal .modal-header h2,
  .qr-display-modal .modal-header-ltr h2 {
    font-size: 1.6rem !important;
  }

  .qr-display-container {
    padding: 1.5rem;
  }

  /* Repair Info Modal Responsive */
  .repair-info-modal .modal-header,
  .repair-info-modal .modal-header-ltr {
    padding: 1.5rem !important;
  }

  .repair-info-modal .modal-header h2,
  .repair-info-modal .modal-header-ltr h2 {
    font-size: 1.6rem !important;
  }

  /* Admin Confirmation Modal Responsive */
  .admin-confirmation-modal {
    width: 95%;
    max-height: 90vh;
  }

  .admin-confirmation-modal .modal-header,
  .admin-confirmation-modal .modal-header-ltr {
    padding: 1.5rem !important;
  }

  .admin-confirmation-modal .modal-header h2,
  .admin-confirmation-modal .modal-header-ltr h2 {
    font-size: 1.5rem !important;
  }

  .admin-confirmation-container {
    padding: 1.5rem;
  }

  .admin-confirmation-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .admin-confirmation-actions .btn {
    width: 100%;
  }
}

/* Modern Keyboard Shortcuts Section */
.keyboard-shortcuts-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  border: 1px solid #dee2e6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  max-width: 100%;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.shortcut-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.shortcut-item kbd {
  background: linear-gradient(135deg, #495057, #6c757d);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  font-size: 0.85rem;
  min-width: 50px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 1px solid #343a40;
}

.shortcut-item span {
  font-weight: 500;
  color: #495057;
  font-size: 0.9rem;
}

/* Arabic RTL Support for Shortcuts */
.lang-ar .shortcuts-grid {
  direction: rtl;
}

.lang-ar .shortcut-item {
  flex-direction: row-reverse;
}

/* French and English LTR Support for Shortcuts */
.lang-fr .shortcuts-grid,
.lang-en .shortcuts-grid {
  direction: ltr;
}

.lang-fr .shortcut-item,
.lang-en .shortcut-item {
  flex-direction: row;
}

/* Modern Dashboard Action Buttons */
.dashboard-action-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin: 2rem 0;
  padding: 0 1rem;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 140px;
  font-family: inherit;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.action-btn-sales {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.action-btn-product {
  background: linear-gradient(135deg, #17a2b8, #6f42c1);
  color: white;
}

.action-btn-purchase {
  background: linear-gradient(135deg, #007bff, #6610f2);
  color: white;
}

.btn-shortcut {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.btn-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.btn-text {
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  line-height: 1.3;
}

/* Dashboard Scanner Section - Same Design as Product Modal */
.dashboard-scanner-section {
  margin: 2rem 0;
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.scanner-vertical-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
  max-width: 800px;
  margin: 0 auto; /* Center the container */
}

/* Unified Scanner and LCD Frame - Single Combined Frame */
.dashboard-scanner-lcd-unified {
  margin-bottom: 2rem;
}

.unified-frame {
  display: grid;
  grid-template-columns: 1fr 1fr; /* Equal width side by side */
  gap: 0; /* No gap - unified frame */
  background: linear-gradient(135deg, #2c3e50, #34495e); /* Grey LCD color scheme */
  border-radius: 12px;
  border: 3px solid #1a252f;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  overflow: hidden; /* Ensure children fit within rounded corners */
  min-height: 200px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Language-specific layout for unified frame */
/* French and English - Scanner left, LCD right */
.lang-fr .unified-frame,
.lang-en .unified-frame {
  grid-template-areas: "scanner lcd";
}

.lang-fr .scanner-section,
.lang-en .scanner-section {
  grid-area: scanner;
}

.lang-fr .lcd-section,
.lang-en .lcd-section {
  grid-area: lcd;
}

/* Arabic - LCD left, Scanner right */
.lang-ar .unified-frame {
  grid-template-areas: "lcd scanner";
}

.lang-ar .scanner-section {
  grid-area: scanner;
}

.lang-ar .lcd-section {
  grid-area: lcd;
}

/* Scanner Section in Unified Frame */
.unified-frame .scanner-section {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  border-right: 2px solid #1a252f; /* Separator line */
}

.lang-ar .unified-frame .scanner-section {
  border-right: none;
  border-left: 2px solid #1a252f; /* Separator line for Arabic */
}

/* LCD Section in Unified Frame */
.unified-frame .lcd-section {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Unified Scanner and LCD Frame - Single Combined Frame */
.dashboard-scanner-lcd-unified {
  margin-bottom: 2rem;
}

.unified-frame {
  display: grid;
  grid-template-columns: 1fr 1fr; /* Equal width side by side */
  gap: 0; /* No gap - unified frame */
  background: linear-gradient(135deg, #2c3e50, #34495e); /* Grey LCD color scheme */
  border-radius: 12px;
  border: 3px solid #1a252f;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  overflow: hidden; /* Ensure children fit within rounded corners */
  min-height: 200px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Language-specific layout for unified frame */
/* French and English - LCD LEFT, Scanner RIGHT */
.lang-fr .unified-frame,
.lang-en .unified-frame {
  grid-template-areas: "lcd scanner";
}

.lang-fr .scanner-section,
.lang-en .scanner-section {
  grid-area: scanner;
}

.lang-fr .lcd-section,
.lang-en .lcd-section {
  grid-area: lcd;
}

/* Arabic - Scanner RIGHT, LCD LEFT */
.lang-ar .unified-frame {
  grid-template-areas: "lcd scanner";
}

.lang-ar .scanner-section {
  grid-area: scanner;
}

.lang-ar .lcd-section {
  grid-area: lcd;
}

/* Scanner Section in Unified Frame */
.unified-frame .scanner-section {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

/* Separator line between LCD and scanner */
.lang-fr .unified-frame .lcd-section,
.lang-en .unified-frame .lcd-section {
  border-right: 2px solid #1a252f; /* Separator line for FR/EN */
}

.lang-ar .unified-frame .lcd-section {
  border-right: 2px solid #1a252f; /* Separator line for Arabic */
}

/* LCD Section in Unified Frame */
.unified-frame .lcd-section {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Scanner Title Styling - Clear fonts for grey background */
.unified-frame .scanner-section h3 {
  margin: 0 0 15px 0;
  color: #ffffff; /* White text for better visibility */
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5); /* Text shadow for clarity */
}

/* Active Status Indicator */
.scanner-status-active {
  color: #00ff41 !important; /* Bright green for active status */
  font-size: 0.9rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(0, 255, 65, 0.8);
  animation: activeGlow 2s infinite alternate;
}

@keyframes activeGlow {
  0% {
    text-shadow: 0 0 10px rgba(0, 255, 65, 0.8);
    color: #00ff41;
  }
  100% {
    text-shadow: 0 0 20px rgba(0, 255, 65, 1);
    color: #33ff66;
  }
}

/* Barcode Input Styling for Unified Frame */
.unified-frame .barcode-input-container {
  position: relative;
  display: flex;
  align-items: center;
  margin: 15px 0;
}

.unified-frame .barcode-icon {
  position: absolute;
  left: 15px;
  font-size: 22px;
  color: #00ff41;
  pointer-events: none;
  z-index: 2;
  text-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
}

.unified-frame .barcode-input {
  width: 100%;
  padding: 14px 16px 14px 55px;
  background: linear-gradient(135deg, #0f1419, #1a252f);
  border: 2px solid #00ff41;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #00ff41;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.5);
}

.unified-frame .barcode-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25), inset 0 2px 10px rgba(0, 0, 0, 0.5);
  color: #ffffff;
  background: linear-gradient(135deg, #1a252f, #2c3e50);
}

.unified-frame .barcode-input::placeholder {
  color: #7fb3d3;
  font-style: italic;
}

/* Barcode Actions Styling */
.unified-frame .barcode-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  justify-content: center;
}

.unified-frame .barcode-actions .btn {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.unified-frame .barcode-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Help text styling */
.unified-frame .barcode-help {
  color: #b0c4de !important; /* Light blue-grey for better visibility */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.scanner-row-container {
  display: grid;
  grid-template-columns: 1fr 1fr; /* Equal width side by side */
  gap: 2rem;
  align-items: stretch; /* Make both items same height */
  max-width: 1200px;
  margin: 0 auto; /* Center the container */
}

/* Language-specific scanner layout */
/* Arabic - Scanner left, LCD right (current layout) */
.lang-ar .scanner-row-container {
  grid-template-areas: "scanner lcd";
}

.lang-ar .dashboard-barcode-scanner {
  grid-area: scanner;
}

.lang-ar .dashboard-lcd-display {
  grid-area: lcd;
}

/* French and English - LCD right, Scanner left */
.lang-fr .scanner-row-container,
.lang-en .scanner-row-container {
  grid-template-areas: "scanner lcd";
}

.lang-fr .dashboard-barcode-scanner,
.lang-en .dashboard-barcode-scanner {
  grid-area: scanner;
}

.lang-fr .dashboard-lcd-display,
.lang-en .dashboard-lcd-display {
  grid-area: lcd;
}

/* Unified Scanner and LCD Frame - Single Row Layout */
.dashboard-scanner-lcd-unified {
  margin-bottom: 2rem;
}

.unified-scanner-lcd-container {
  display: grid;
  grid-template-columns: 1fr 1fr; /* Equal width side by side */
  gap: 0; /* No gap - unified frame */
  background: linear-gradient(135deg, #2c3e50, #34495e); /* Grey LCD color scheme */
  border-radius: 12px;
  border: 3px solid #1a252f;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  overflow: hidden; /* Ensure children fit within rounded corners */
  min-height: 200px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Language-specific layout for unified frame */
/* French and English - Scanner left, LCD right */
.lang-fr .unified-scanner-lcd-container,
.lang-en .unified-scanner-lcd-container {
  grid-template-areas: "scanner lcd";
}

.lang-fr .barcode-scanner-section,
.lang-en .barcode-scanner-section {
  grid-area: scanner;
}

.lang-fr .lcd-display-section,
.lang-en .lcd-display-section {
  grid-area: lcd;
}

/* Arabic - Scanner right, LCD left */
.lang-ar .unified-scanner-lcd-container {
  grid-template-areas: "lcd scanner";
}

.lang-ar .barcode-scanner-section {
  grid-area: scanner;
}

.lang-ar .lcd-display-section {
  grid-area: lcd;
}

/* Barcode Scanner Section in Unified Frame */
.unified-scanner-lcd-container .barcode-scanner-section {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  border-right: 2px solid #1a252f; /* Separator line */
}

.lang-ar .unified-scanner-lcd-container .barcode-scanner-section {
  border-right: none;
  border-left: 2px solid #1a252f; /* Separator line for Arabic */
}

/* Dashboard Barcode Scanner - Matching LCD Display Frame Design */
.dashboard-barcode-scanner {
  background: rgb(49, 69, 89);
  padding: 1.5rem;
  border-radius: 12px;
  border: 3px solid #1a252f;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  min-height: 200px; /* Same height as LCD */
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

.dashboard-barcode-scanner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 255, 65, 0.1), rgba(0, 123, 255, 0.1));
  border-radius: 8px;
  pointer-events: none;
}

.dashboard-barcode-scanner h3 {
  margin: 0 0 15px 0;
  color: #00ff41;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 1;
}

/* Dashboard Barcode Input Container - Enhanced Design */
.dashboard-barcode-scanner .barcode-input-container {
  position: relative;
  display: flex;
  align-items: center;
  margin: 15px 0;
  z-index: 1;
}

.dashboard-barcode-scanner .barcode-icon {
  position: absolute;
  left: 15px;
  font-size: 22px;
  color: #00ff41;
  pointer-events: none;
  z-index: 2;
  text-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
}

.dashboard-barcode-scanner .barcode-input {
  width: 100%;
  padding: 14px 16px 14px 55px;
  background: linear-gradient(135deg, #0f1419, #1a252f);
  border: 2px solid #00ff41;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #00ff41;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.5);
}

.dashboard-barcode-scanner .barcode-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25), inset 0 2px 10px rgba(0, 0, 0, 0.5);
  color: #ffffff;
  background: linear-gradient(135deg, #1a252f, #2c3e50);
}

.dashboard-barcode-scanner .barcode-input::placeholder {
  color: #7fb3d3;
  font-style: italic;
}

/* Dashboard Barcode Actions - Enhanced Design */
.dashboard-barcode-scanner .barcode-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  justify-content: center;
  z-index: 1;
}

.dashboard-barcode-scanner .barcode-actions .btn {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.dashboard-barcode-scanner .barcode-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Dashboard LCD Display - Matching Scanner Size */
.dashboard-lcd-display {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border: 3px solid #1a252f;
  min-height: 200px; /* Same height as scanner */
}

.lcd-screen {
  background: #0f1419;
  border-radius: 8px;
  padding: 1rem;
  min-height: 160px; /* Matching scanner inner height */
  border: 2px inset #2c3e50;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.5);
  font-family: 'Courier New', monospace;
}

.lcd-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #333;
  margin-bottom: 1rem;
}

.lcd-title {
  color: #00ff41;
  font-size: 0.9rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.lcd-status {
  color: #00ff41;
  font-size: 1.2rem;
  animation: blink 2s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.lcd-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 80px;
}

.product-display {
  text-align: left;
  width: 100%;
  color: #00ff41;
}

.product-name {
  color: #00ff41;
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 0.8rem;
  word-wrap: break-word;
  line-height: 1.3;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.product-price-row,
.product-quantity-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.3rem 0;
  border-bottom: 1px dotted #333;
}

.price-label,
.qty-label {
  color: #7fb3d3;
  font-size: 0.9rem;
  font-weight: normal;
}

.product-price {
  color: #ffffff;
  font-size: 1.3rem;
  font-weight: bold;
  background: linear-gradient(135deg, #2c3e50, #34495e);
  padding: 0.3rem 0.8rem;
  border-radius: 4px;
  border: 1px solid #4a6741;
  text-align: center;
  min-width: 120px;
}

/* Bigger price display for dashboard */
.product-price-display {
  text-align: center;
  margin: 1rem 0;
}

.product-price-big {
  color: #ffffff;
  font-size: 2.2rem; /* Much bigger */
  font-weight: bold;
  background: linear-gradient(135deg, #2c3e50, #34495e);
  padding: 0.8rem 1.2rem;
  border-radius: 8px;
  border: 2px solid #00ff41;
  text-align: center;
  min-width: 200px;
  margin: 0 auto;
  box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
  animation: priceGlow 2s infinite alternate;
}

@keyframes priceGlow {
  0% { box-shadow: 0 0 10px rgba(0, 255, 65, 0.3); }
  100% { box-shadow: 0 0 20px rgba(0, 255, 65, 0.6); }
}

.placeholder-price-big {
  color: #444;
  font-size: 2.2rem; /* Much bigger */
  font-weight: bold;
  margin: 1rem auto;
  background: #222;
  padding: 0.8rem 1.2rem;
  border-radius: 8px;
  border: 2px solid #333;
  text-align: center;
  min-width: 200px;
}

.product-quantity {
  color: #00ff41;
  font-size: 1.1rem;
  font-weight: bold;
  background: #1a252f;
  padding: 0.3rem 0.8rem;
  border-radius: 4px;
  border: 1px solid #00ff41;
  text-align: center;
  min-width: 60px;
}

.display-placeholder {
  text-align: center;
  width: 100%;
  color: #666;
}

.placeholder-text {
  color: #555;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  font-style: italic;
}

.placeholder-price {
  color: #444;
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  background: #222;
  padding: 0.3rem 0.8rem;
  border-radius: 4px;
  border: 1px solid #333;
}

.placeholder-quantity {
  color: #444;
  font-size: 0.9rem;
  font-weight: normal;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-action-buttons {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .action-btn {
    min-height: 100px;
    padding: 1.5rem 1rem;
  }

  .btn-icon {
    font-size: 2rem;
  }

  .btn-text {
    font-size: 0.9rem;
  }

  /* Unified frame responsive */
  .unified-frame {
    grid-template-columns: 1fr;
    grid-template-areas: "scanner" "lcd" !important;
    max-width: 100%;
  }

  .unified-frame .scanner-section,
  .unified-frame .lcd-section {
    border-right: none !important;
    border-left: none !important;
    border-bottom: 2px solid #1a252f;
  }

  .unified-frame .lcd-section {
    border-bottom: none;
  }

  .scanner-row-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-width: 100%;
  }

  .scanner-vertical-container {
    gap: 1rem;
    max-width: 100%;
  }

  .dashboard-barcode-scanner {
    min-height: 180px;
  }

  .dashboard-lcd-display {
    min-height: 180px;
  }

  .lcd-screen {
    min-height: 140px;
  }
}

@media (max-width: 768px) {
  .dashboard-action-buttons {
    padding: 0;
    gap: 0.75rem;
  }

  .action-btn {
    min-height: 80px;
    padding: 1rem;
  }

  .btn-icon {
    font-size: 1.5rem;
  }

  .btn-text {
    font-size: 0.8rem;
  }

  /* Unified frame mobile responsive */
  .unified-frame .scanner-section,
  .unified-frame .lcd-section {
    padding: 1rem;
    min-height: 160px;
  }

  .unified-frame .scanner-section h3 {
    font-size: 0.9rem;
  }

  .unified-frame .barcode-input {
    font-size: 14px;
    padding: 12px 14px 12px 50px;
  }

  .unified-frame .barcode-icon {
    font-size: 18px;
    left: 12px;
  }

  .unified-frame .lcd-screen {
    min-height: 120px;
    padding: 0.8rem;
  }

  .product-name {
    font-size: 1rem;
  }

  .product-price-big {
    font-size: 1.8rem;
    padding: 0.6rem 1rem;
    min-width: 160px;
  }

  .placeholder-price-big {
    font-size: 1.8rem;
    padding: 0.6rem 1rem;
    min-width: 160px;
  }

  .product-quantity {
    font-size: 1rem;
    min-width: 50px;
  }

  .dashboard-scanner-section {
    padding: 1rem;
  }

  .scanner-container {
    margin-left: 0; /* Remove margin on mobile */
  }

  .dashboard-barcode-scanner {
    min-height: 160px;
  }

  .dashboard-lcd-display {
    padding: 1rem;
    min-height: 160px;
  }

  .lcd-screen {
    min-height: 120px;
    padding: 0.8rem;
  }

  .product-price {
    font-size: 1.1rem;
    min-width: 100px;
  }
}

/* RTL Support for Dashboard */
.lang-ar .dashboard-action-buttons {
  direction: rtl;
}

.lang-ar .btn-shortcut {
  right: auto;
  left: 12px;
}

.lang-ar .scanner-container {
  direction: rtl;
}

/* LTR Support for Dashboard */
.lang-fr .dashboard-action-buttons,
.lang-en .dashboard-action-buttons {
  direction: ltr;
}

.lang-fr .scanner-container,
.lang-en .scanner-container {
  direction: ltr;
}

/* Responsive Design for Shortcuts */
@media (max-width: 768px) {
  .shortcuts-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .shortcut-item {
    padding: 0.6rem 0.8rem;
  }

  .shortcut-item kbd {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
    min-width: 45px;
  }

  .shortcut-item span {
    font-size: 0.85rem;
  }
}

/* Bulk Actions Styling */
.bulk-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bulk-info {
  font-weight: bold;
  color: #856404;
  font-size: 0.9rem;
}

.bulk-actions .btn {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.bulk-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Table Checkbox Styling */
.data-table input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #007bff;
}

.data-table th input[type="checkbox"] {
  accent-color: #28a745;
}

/* Arabic RTL Support for Bulk Actions */
.lang-ar .bulk-actions {
  direction: rtl;
  flex-direction: row-reverse;
}

/* French and English LTR Support for Bulk Actions */
.lang-fr .bulk-actions,
.lang-en .bulk-actions {
  direction: ltr;
  flex-direction: row;
}

/* Modern Supplier Parts Management Modals */
.modern-payment-modal,
.modern-transaction-modal,
.modern-edit-modal,
.modern-delete-modal {
  max-width: 1400px;
  width: 98%;
  max-height: 95vh;
  overflow-y: auto;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

/* Modern Search Section for Transaction Modal */
.modern-search-section {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e9ecef;
}

.search-controls-modern {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
}

.search-input-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.modern-search-input {
  width: 100%;
  padding: 12px 45px 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  font-size: 16px;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.modern-search-input:focus {
  outline: none;
  border-color: #498C8A;
  box-shadow: 0 0 0 3px rgba(73, 140, 138, 0.1);
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 18px;
  pointer-events: none; /* Prevent icon from blocking cursor focus */
  z-index: 1;
}

/* Large Responsive Table Styles */
.table-section-large {
  margin: 1.5rem 0;
}

.large-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.table-wrapper-responsive {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 500px;
}

.large-responsive-table {
  width: 100%;
  min-width: 800px;
  font-size: 14px;
}

.table-header-sticky {
  position: sticky;
  top: 0;
  background: #498C8A;
  z-index: 10;
}

.table-header-sticky th {
  background: #498C8A;
  color: white;
  padding: 15px 12px;
  font-weight: 600;
  text-align: left;
  border-bottom: 2px solid #3a7a78;
}

.table-row-hover:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.large-responsive-table td {
  padding: 12px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.status-badge-modern {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-paid {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* Modern Bottom Actions */
.modern-bottom-actions {
  margin-top: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 12px;
  border-top: 1px solid #e9ecef;
}

.action-buttons-container {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-modern-clean {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 140px;
  justify-content: center;
}

.btn-print {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-print:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-print-filtered {
  background: linear-gradient(135deg, #17a2b8, #20c997);
  color: white;
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.btn-print-filtered:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
}

.btn-close {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.btn-close:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

/* Compact Delete Modal Styles */
.compact-delete-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 420px;
  width: 90%;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

.compact-header {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  padding: 1.5rem;
  text-align: center;
  position: relative;
}

.header-icon-large {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  display: block;
}

.header-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn-compact {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn-compact:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.compact-content {
  padding: 1.5rem;
  text-align: center;
}

.warning-message-compact {
  margin-bottom: 1.5rem;
}

.supplier-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: #dc3545;
  margin: 0 0 0.5rem 0;
  padding: 0.5rem;
  background: #f8d7da;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

.warning-text {
  color: #6c757d;
  margin: 0;
  font-size: 0.9rem;
}

.stats-compact {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-item {
  font-size: 0.85rem;
  font-weight: 600;
  color: #495057;
}

.passcode-input-compact {
  margin-bottom: 1rem;
}

.compact-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  text-align: center;
  transition: all 0.3s ease;
}

.compact-input:focus {
  outline: none;
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.compact-actions {
  display: flex;
  gap: 0.75rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.btn-compact {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-cancel {
  background: #6c757d;
  color: white;
}

.btn-cancel:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.btn-delete {
  background: #dc3545;
  color: white;
}

.btn-delete:hover:not(:disabled) {
  background: #c82333;
  transform: translateY(-1px);
}

.btn-delete:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Compact Edit Modal Styles */
.compact-edit-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 520px;
  width: 90%;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

.compact-edit-header {
  background: linear-gradient(135deg, #498C8A, #3a7a78);
  color: white;
  padding: 1.5rem;
  text-align: center;
  position: relative;
}

.header-icon-edit {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

.header-title-edit {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.compact-edit-content {
  padding: 1.5rem;
}

.error-message-compact {
  text-align: center;
  color: #dc3545;
  font-weight: 600;
  padding: 2rem;
}

.form-fields-compact {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.field-row {
  display: flex;
  gap: 1rem;
}

.field-compact {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label-compact {
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.input-compact {
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.input-compact:focus {
  outline: none;
  border-color: #498C8A;
  box-shadow: 0 0 0 3px rgba(73, 140, 138, 0.1);
}

.compact-edit-actions {
  display: flex;
  gap: 0.75rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.btn-save {
  background: #498C8A;
  color: white;
}

.btn-save:hover {
  background: #3a7a78;
  transform: translateY(-1px);
}

/* Repair Action Buttons - Header Color Matching */
.repair-action-buttons-styled .btn {
  border: 2px solid transparent;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-repair-view {
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  color: white;
  border-color: #46ACC2;
}

.btn-repair-view:hover {
  background: linear-gradient(135deg, #3a8fa3, #2e7282);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(70, 172, 194, 0.3);
}

.btn-repair-edit {
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  color: white;
  border-color: #46ACC2;
}

.btn-repair-edit:hover {
  background: linear-gradient(135deg, #3a8fa3, #2e7282);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(70, 172, 194, 0.3);
}

.btn-repair-paste {
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  color: white;
  border-color: #46ACC2;
}

.btn-repair-paste:hover {
  background: linear-gradient(135deg, #3a8fa3, #2e7282);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(70, 172, 194, 0.3);
}

.btn-repair-print {
  background: linear-gradient(135deg, #46ACC2, #3a8fa3);
  color: white;
  border-color: #46ACC2;
}

.btn-repair-print:hover {
  background: linear-gradient(135deg, #3a8fa3, #2e7282);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(70, 172, 194, 0.3);
}

.btn-repair-delete {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border-color: #dc3545;
}

.btn-repair-delete:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* Status Badge for Waiting */
.status-waitingForClient {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Simple Waiting Status - Just Text */
.status-waiting-simple {
  background: transparent;
  color: #498C8A;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid #498C8A;
}

/* Embedded Action Buttons - Header Color Matching with Enhanced Icon Backgrounds */
.repair-action-buttons-embedded .btn {
  border: 2px solid transparent;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.repair-action-buttons-embedded .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.repair-action-buttons-embedded .btn:hover::before {
  opacity: 1;
}

.btn-repair-view-embedded {
  background: linear-gradient(135deg, #498C8A, #3a7a78);
  color: white;
  border-color: #498C8A;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.btn-repair-view-embedded:hover {
  background: linear-gradient(135deg, #3a7a78, #2e6866);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(73, 140, 138, 0.4), inset 0 0 0 1px rgba(255, 255, 255, 0.3);
}

.btn-repair-edit-embedded {
  background: linear-gradient(135deg, #5a9c9a, #498C8A);
  color: white;
  border-color: #498C8A;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.btn-repair-edit-embedded:hover {
  background: linear-gradient(135deg, #498C8A, #3a7a78);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(73, 140, 138, 0.4), inset 0 0 0 1px rgba(255, 255, 255, 0.3);
}

.btn-repair-paste-embedded {
  background: linear-gradient(135deg, #6bacaa, #5a9c9a);
  color: white;
  border-color: #498C8A;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.btn-repair-paste-embedded:hover {
  background: linear-gradient(135deg, #5a9c9a, #498C8A);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(73, 140, 138, 0.4), inset 0 0 0 1px rgba(255, 255, 255, 0.3);
}

.btn-repair-print-embedded {
  background: linear-gradient(135deg, #7cbcba, #6bacaa);
  color: white;
  border-color: #498C8A;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.btn-repair-print-embedded:hover {
  background: linear-gradient(135deg, #6bacaa, #5a9c9a);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(73, 140, 138, 0.4), inset 0 0 0 1px rgba(255, 255, 255, 0.3);
}

.btn-repair-delete-embedded {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border-color: #dc3545;
}

.btn-repair-delete-embedded:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* Language Direction Classes for Edit Supplier Modal */
.modal-ltr {
  direction: ltr;
  text-align: left;
}

.modal-rtl {
  direction: rtl;
  text-align: right;
}

.header-ltr {
  flex-direction: row;
}

.header-rtl {
  flex-direction: row-reverse;
}

.content-ltr {
  direction: ltr;
}

.content-rtl {
  direction: rtl;
}

.fields-ltr {
  direction: ltr;
}

.fields-rtl {
  direction: rtl;
}

.row-ltr {
  flex-direction: row;
}

.row-rtl {
  flex-direction: row-reverse;
}

.label-ltr {
  text-align: left;
}

.label-rtl {
  text-align: right;
}

.input-ltr {
  text-align: left;
}

.input-rtl {
  text-align: right;
}

.actions-ltr {
  flex-direction: row;
}

.actions-rtl {
  flex-direction: row-reverse;
}

/* Responsive adjustments for compact modals */
@media (max-width: 768px) {
  .compact-delete-modal,
  .compact-edit-modal {
    width: 95%;
    max-width: none;
  }

  .field-row {
    flex-direction: column;
    gap: 0.75rem;
  }

  .compact-edit-content,
  .compact-content {
    padding: 1rem;
  }

  .compact-edit-actions,
  .compact-actions {
    padding: 1rem;
  }
}

.payment-header,
.modal-header-ltr.payment-header {
  background: linear-gradient(135deg, #e67e22, #f39c12) !important;
  color: white !important;
  padding: 2rem;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 8px 32px rgba(230, 126, 34, 0.3);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.payment-body,
.transaction-body,
.edit-body,
.delete-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  min-height: 0;
}

.transaction-header,
.modal-header-ltr.transaction-header {
  background: linear-gradient(135deg, #3498db, #2980b9) !important;
  color: white !important;
  padding: 2rem;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 8px 32px rgba(52, 152, 219, 0.3);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.edit-header,
.modal-header-ltr.edit-header {
  background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
  color: white !important;
  padding: 2rem;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 8px 32px rgba(39, 174, 96, 0.3);
  position: relative;
  overflow: hidden;
}

.delete-header,
.modal-header-ltr.delete-header {
  background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
  color: white !important;
  padding: 2rem;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 8px 32px rgba(231, 76, 60, 0.3);
  position: relative;
  overflow: hidden;
}

.payment-header::before,
.transaction-header::before,
.edit-header::before,
.delete-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  pointer-events: none;
}

.payment-header h2,
.transaction-header h2,
.edit-header h2,
.delete-header h2 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 800;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.3px;
}

.payment-body,
.transaction-body,
.edit-body,
.delete-body {
  padding: 2.5rem;
  max-height: 65vh;
  overflow-y: hidden;
  background: #f8f9fa;
}

.transactions-section {
  margin-bottom: 2.5rem;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
}

/* Modern responsive tables for supplier modal tables */
.modern-payment-modal .table-container,
.modern-transaction-modal .table-container,
.modern-edit-modal .table-container,
.modern-delete-modal .table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow-x: auto;
  overflow-y: auto;
  max-height: 65vh;
  position: relative;
  width: 100%;
}

.modern-payment-modal .data-table,
.modern-transaction-modal .data-table,
.modern-edit-modal .data-table,
.modern-delete-modal .data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
  background: white;
}

.modern-payment-modal .data-table thead,
.modern-transaction-modal .data-table thead,
.modern-edit-modal .data-table thead,
.modern-delete-modal .data-table thead {
  position: sticky;
  top: 0;
  z-index: 10;
  background: linear-gradient(135deg, #037171, #00b9ae);
  color: white;
}

.modern-payment-modal .data-table thead th,
.modern-transaction-modal .data-table thead th,
.modern-edit-modal .data-table thead th,
.modern-delete-modal .data-table thead th {
  padding: 1rem 0.8rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.9rem;
  border: none;
  white-space: nowrap;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.modern-payment-modal .data-table tbody tr,
.modern-transaction-modal .data-table tbody tr,
.modern-edit-modal .data-table tbody tr,
.modern-delete-modal .data-table tbody tr {
  border-bottom: 1px solid #f1f3f4;
  transition: all 0.2s ease;
}

.modern-payment-modal .data-table tbody tr:hover,
.modern-transaction-modal .data-table tbody tr:hover,
.modern-edit-modal .data-table tbody tr:hover,
.modern-delete-modal .data-table tbody tr:hover {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modern-payment-modal .data-table tbody td,
.modern-transaction-modal .data-table tbody td,
.modern-edit-modal .data-table tbody td,
.modern-delete-modal .data-table tbody td {
  padding: 1rem 0.8rem;
  border: none;
  vertical-align: middle;
  font-size: 0.9rem;
  color: #2c3e50;
}

.modern-payment-modal .transactions-section,
.modern-transaction-modal .transactions-section,
.modern-edit-modal .transactions-section,
.modern-delete-modal .transactions-section {
  margin-bottom: 2rem;
}

.transactions-section h3 {
  color: #037171;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  font-weight: 800;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.payment-section {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 2rem;
  border-radius: 12px;
  margin-top: 1.5rem;
  border: 2px solid #037171;
  box-shadow: 0 4px 16px rgba(3, 113, 113, 0.1);
}

.payment-summary {
  margin-bottom: 1.5rem;
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.summary-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

/* Modern search controls for supplier modals */
.modern-payment-modal .search-section,
.modern-transaction-modal .search-section {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.modern-payment-modal .search-controls,
.modern-transaction-modal .search-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.modern-payment-modal .search-input,
.modern-transaction-modal .search-input {
  flex: 1;
  min-width: 250px;
  padding: 0.8rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: white;
}

.modern-payment-modal .search-input:focus,
.modern-transaction-modal .search-input:focus {
  border-color: #037171;
  box-shadow: 0 0 0 3px rgba(3, 113, 113, 0.1);
  outline: none;
}

/* Enhanced payment section styling */
.modern-payment-modal .payment-section {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  padding: 2rem;
  border-radius: 12px;
  margin-top: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 2px solid #037171;
}

.modern-payment-modal .payment-summary {
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.modern-payment-modal .summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
  border-left: 4px solid #037171;
}

.modern-payment-modal .summary-item .label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
}

.modern-payment-modal .summary-item .value {
  font-weight: 700;
  color: #037171;
  font-size: 1.2rem;
}

.modern-payment-modal .payment-input {
  margin-bottom: 2rem;
}

.modern-payment-modal .payment-input label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.modern-payment-modal .payment-input input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.modern-payment-modal .payment-input input:focus {
  border-color: #037171;
  box-shadow: 0 0 0 3px rgba(3, 113, 113, 0.1);
  outline: none;
}

.summary-item .label {
  font-weight: 700;
  color: #495057;
  font-size: 1rem;
}

.summary-item .value {
  font-weight: 800;
  color: #037171;
  font-size: 1.2rem;
  background: rgba(3, 113, 113, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 6px;
}

.payment-input {
  margin-top: 1.5rem;
}

.payment-input input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #037171;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.payment-input input:focus {
  outline: none;
  border-color: #00b9ae;
  box-shadow: 0 0 0 3px rgba(3, 113, 113, 0.2);
  transform: translateY(-1px);
}

/* Modal Action Buttons */
.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 2px solid #e9ecef;
}

.modal-actions .btn-modern {
  padding: 0.8rem 1.5rem;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 700;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modal-actions .btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Responsive Design for Supplier Modals */
@media (max-width: 1200px) {
  .modern-payment-modal,
  .modern-transaction-modal,
  .modern-edit-modal,
  .modern-delete-modal {
    width: 95%;
    max-width: 900px;
  }

  .payment-body,
  .transaction-body,
  .edit-body,
  .delete-body {
    padding: 2rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
  }

  .transactions-section {
    padding: 1.5rem;
    overflow: visible;
    flex: 1;
  }

  .payment-section {
    padding: 1.5rem;
  }

  .modern-payment-modal .table-container,
  .modern-transaction-modal .table-container {
    max-height: 55vh;
    overflow-x: auto;
    overflow-y: auto;
  }

  .modern-payment-modal .search-controls,
  .modern-transaction-modal .search-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .modern-payment-modal .search-input,
  .modern-transaction-modal .search-input {
    min-width: 100%;
    margin-bottom: 1rem;
  }
}

@media (max-width: 768px) {
  .modern-payment-modal,
  .modern-transaction-modal,
  .modern-edit-modal,
  .modern-delete-modal {
    width: 98%;
    max-height: 95vh;
    margin: 1rem;
  }

  .payment-header,
  .modal-header-ltr.payment-header,
  .transaction-header,
  .modal-header-ltr.transaction-header,
  .edit-header,
  .modal-header-ltr.edit-header,
  .delete-header,
  .modal-header-ltr.delete-header {
    padding: 1.5rem !important;
    border-radius: 15px 15px 0 0 !important;
  }

  .payment-header h2,
  .modal-header-ltr.payment-header h2,
  .transaction-header h2,
  .modal-header-ltr.transaction-header h2,
  .edit-header h2,
  .modal-header-ltr.edit-header h2,
  .delete-header h2,
  .modal-header-ltr.delete-header h2 {
    font-size: 1.4rem !important;
    color: white !important;
  }

  .modern-payment-modal .table-container,
  .modern-transaction-modal .table-container {
    max-height: 40vh;
  }

  .modern-payment-modal .data-table thead th,
  .modern-transaction-modal .data-table thead th {
    padding: 0.8rem 0.5rem;
    font-size: 0.8rem;
  }

  .modern-payment-modal .data-table tbody td,
  .modern-transaction-modal .data-table tbody td {
    padding: 0.8rem 0.5rem;
    font-size: 0.8rem;
  }

  .modern-payment-modal .search-section,
  .modern-transaction-modal .search-section {
    padding: 1rem;
  }

  .modern-payment-modal .search-controls,
  .modern-transaction-modal .search-controls {
    flex-direction: column;
    gap: 0.8rem;
  }

  .modern-payment-modal .search-input,
  .modern-transaction-modal .search-input {
    min-width: 100%;
    padding: 0.8rem;
    font-size: 0.9rem;
  }

  .modern-payment-modal .payment-section {
    padding: 1.5rem;
    margin-top: 1rem;
  }

  .modern-payment-modal .summary-item {
    padding: 0.8rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .modern-payment-modal .summary-item .label,
  .modern-payment-modal .summary-item .value {
    font-size: 1rem;
  }

  .payment-body,
  .transaction-body,
  .edit-body,
  .delete-body {
    padding: 1.5rem;
    max-height: 75vh;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
  }

  .transactions-section {
    padding: 1rem;
    margin-bottom: 1.5rem;
    overflow: visible;
    flex: 1;
  }

  .transactions-section h3 {
    font-size: 1.2rem;
  }

  .payment-section {
    padding: 1rem;
  }

  .payment-summary {
    padding: 1rem;
  }

  .summary-item .value {
    font-size: 1rem;
  }

  .modal-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .modal-actions .btn-modern {
    width: 100%;
    justify-content: center;
    padding: 1rem;
  }
}

/* Comprehensive Responsive Landscape Design */
@media screen and (orientation: landscape) {
  /* Repair Management Page Landscape Optimizations */
  .repairs-page {
    padding: 1.5rem;
    min-height: 100vh;
  }

  .repair-orders-header {
    margin: 1rem 0;
    padding: 1.5rem;
  }

  .orders-main-title {
    font-size: 1.6rem;
  }

  .orders-subtitle {
    font-size: 0.9rem;
  }

  /* Suppliers Section Landscape */
  .suppliers-parts-reparation-section {
    margin-top: 2rem;
    padding: 1.5rem;
  }

  .suppliers-parts-reparation-section .section-header {
    padding: 1.5rem;
  }

  .suppliers-parts-reparation-section .section-header h2 {
    font-size: 1.6rem;
  }

  /* Table Containers Landscape */
  .repair-orders-table-container,
  .suppliers-table-container {
    max-height: 50vh;
    overflow-y: auto;
  }

  .table-body-scrollable {
    max-height: 45vh;
  }

  /* Modal Landscape Optimizations */
  .modern-payment-modal,
  .modern-transaction-modal,
  .modern-edit-modal,
  .modern-delete-modal {
    max-height: 85vh;
    width: 90%;
    max-width: 1100px;
  }

  .payment-body,
  .transaction-body,
  .edit-body,
  .delete-body {
    max-height: 60vh;
    padding: 2rem;
    overflow-y: auto;
    flex: 1;
  }

  /* Action Buttons Landscape */
  .repair-action-buttons {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    padding: 1.5rem;
  }

  .header-actions-section {
    gap: 0.6rem;
  }

  .header-actions-section .btn-modern {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
}

/* Tablet Landscape Specific (768px - 1024px) */
@media screen and (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .repairs-page {
    padding: 1rem;
  }

  .repair-orders-header {
    flex-direction: row;
    gap: 1rem;
    padding: 1.2rem;
  }

  .orders-main-title {
    font-size: 1.5rem;
  }

  .header-actions-section {
    flex-direction: row;
    gap: 0.5rem;
  }

  .header-actions-section .btn-modern {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .suppliers-parts-reparation-section .section-header {
    flex-direction: row;
    gap: 1rem;
  }

  .suppliers-parts-reparation-section .header-actions {
    flex-direction: row;
    gap: 0.5rem;
  }

  .repair-action-buttons {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.8rem;
    padding: 1rem;
  }

  /* Table optimizations for tablet landscape */
  .table-body-scrollable {
    max-height: 40vh;
  }

  .modern-payment-modal,
  .modern-transaction-modal,
  .modern-edit-modal,
  .modern-delete-modal {
    max-height: 80vh;
    width: 85%;
  }

  .payment-body,
  .transaction-body,
  .edit-body,
  .delete-body {
    max-height: 55vh;
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
  }
}

/* Mobile Landscape Specific (up to 767px) */
@media screen and (max-width: 767px) and (orientation: landscape) {
  .repairs-page {
    padding: 0.8rem;
  }

  .repair-orders-header {
    margin: 0.5rem 0;
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .orders-main-title {
    font-size: 1.3rem;
  }

  .orders-subtitle {
    font-size: 0.8rem;
  }

  .header-actions-section {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.4rem;
    justify-content: center;
  }

  .header-actions-section .btn-modern {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
    flex: 1;
    min-width: 120px;
  }

  .suppliers-parts-reparation-section {
    margin-top: 1rem;
    padding: 1rem;
  }

  .suppliers-parts-reparation-section .section-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .suppliers-parts-reparation-section .section-header h2 {
    font-size: 1.3rem;
  }

  .suppliers-parts-reparation-section .header-actions {
    flex-direction: row;
    gap: 0.4rem;
    width: 100%;
  }

  .suppliers-parts-reparation-section .header-actions .btn-modern {
    flex: 1;
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .repair-action-buttons {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.6rem;
    padding: 0.8rem;
  }

  /* Compact table design for mobile landscape */
  .table-body-scrollable {
    max-height: 35vh;
  }

  .modern-payment-modal,
  .modern-transaction-modal,
  .modern-edit-modal,
  .modern-delete-modal {
    max-height: 90vh;
    width: 95%;
    margin: 0.5rem;
  }

  .payment-header,
  .modal-header-ltr.payment-header,
  .transaction-header,
  .modal-header-ltr.transaction-header,
  .edit-header,
  .modal-header-ltr.edit-header,
  .delete-header,
  .modal-header-ltr.delete-header {
    padding: 1rem !important;
  }

  .payment-header h2,
  .modal-header-ltr.payment-header h2,
  .transaction-header h2,
  .modal-header-ltr.transaction-header h2,
  .edit-header h2,
  .modal-header-ltr.edit-header h2,
  .delete-header h2,
  .modal-header-ltr.delete-header h2 {
    font-size: 1.2rem !important;
    color: white !important;
  }

  .payment-body,
  .transaction-body,
  .edit-body,
  .delete-body {
    max-height: 60vh;
    padding: 1rem;
    overflow-y: hidden !important;
  }

  .transactions-section {
    padding: 0.8rem;
    margin-bottom: 1rem;
    overflow: visible !important;
  }

  .payment-section {
    padding: 0.8rem;
  }

  .modal-actions {
    flex-direction: row;
    gap: 0.5rem;
  }

  .modal-actions .btn-modern {
    flex: 1;
    padding: 0.6rem;
    font-size: 0.8rem;
  }
}

.payment-input label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
}

.payment-input .form-control {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
}

.payment-input .form-control:focus {
  border-color: #00b9ae;
  box-shadow: 0 0 0 0.2rem rgba(0, 185, 174, 0.25);
}

.search-section {
  margin-bottom: 2rem;
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
}

.search-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
}

.search-input:focus {
  border-color: #00b9ae;
  box-shadow: 0 0 0 0.2rem rgba(0, 185, 174, 0.25);
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.summary-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 2px solid #00b9ae;
  text-align: center;
}

.summary-card.pending {
  border-color: #ffc107;
  background: #fff8e1;
}

.summary-card .card-label {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.summary-card .card-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #00b9ae;
}

.summary-card.pending .card-value {
  color: #f57c00;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.paid {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.edit-form {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
}

.form-group .form-control {
  padding: 0.75rem;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
}

.form-group .form-control:focus {
  border-color: #00b9ae;
  box-shadow: 0 0 0 0.2rem rgba(0, 185, 174, 0.25);
}

.warning-section {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: #fff3cd;
  padding: 1.5rem;
  border-radius: 8px;
  border: 2px solid #ffc107;
  margin-bottom: 2rem;
}

.warning-icon {
  font-size: 2rem;
  color: #856404;
}

.warning-message h3 {
  color: #856404;
  margin-bottom: 0.5rem;
}

.warning-message p {
  color: #856404;
  margin: 0;
}

.warning-text {
  color: #dc3545 !important;
  font-weight: 600;
}

.transaction-summary {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.transaction-summary h4 {
  color: #495057;
  margin-bottom: 1rem;
}

.transaction-summary ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.transaction-summary li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #dee2e6;
  color: #6c757d;
}

.passcode-section {
  background: #e9ecef;
  padding: 1.5rem;
  border-radius: 8px;
}

.passcode-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
}

.passcode-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.passcode-input:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.passcode-hint {
  color: #6c757d;
  font-size: 0.8rem;
}

.payment-footer,
.transaction-footer,
.edit-footer,
.delete-footer {
  padding: 1.5rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.delete-footer .btn-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  border: none;
  color: white;
  font-weight: 600;
}

.delete-footer .btn-danger:hover {
  background: linear-gradient(135deg, #c82333, #bd2130);
  transform: translateY(-1px);
}

.delete-footer .btn-danger:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  text-align: center;
  padding: 2rem;
  color: #dc3545;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Suppliers Table Phone Number Alignment */
.suppliers-table-container table td:nth-child(3) {
  text-align: right !important;
  direction: ltr !important;
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

.suppliers-table-container table th:nth-child(3) {
  text-align: right !important;
}

/* Arabic - Sidebar on the right */
.lang-ar .sidebar {
  right: 0;
  left: auto;
}

/* French and English - Sidebar on the left */
.lang-fr .sidebar,
.lang-en .sidebar {
  left: 0;
  right: auto;
}

.sidebar-header {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.system-logo {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-image {
  max-width: 120px;
  max-height: 120px;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 12px;
  filter: brightness(1.1) contrast(1.1);
  background: rgba(255, 255, 255, 0.1);
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.logo-image:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.logo-fallback {
  font-size: 4rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.system-title h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.system-title span {
  font-size: 0.9rem;
  opacity: 0.8;
}

.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.nav-item {
  width: 100%;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.1rem;
  font-weight: bold;
  font-family: inherit;
}

/* Arabic - Right aligned navigation */
.lang-ar .nav-item {
  text-align: right;
  flex-direction: row;
}

.lang-ar .nav-item:hover {
  padding-right: 2rem;
  padding-left: 1.5rem;
}

.lang-ar .nav-item.active {
  border-right: 4px solid white;
  border-left: none;
}

/* French and English - Left aligned navigation */
.lang-fr .nav-item,
.lang-en .nav-item {
  text-align: left;
  flex-direction: row-reverse;
}

.lang-fr .nav-item:hover,
.lang-en .nav-item:hover {
  padding-left: 2rem;
  padding-right: 1.5rem;
}

.lang-fr .nav-item.active,
.lang-en .nav-item.active {
  border-left: 4px solid white;
  border-right: none;
}

.nav-item:hover {
  background: var(--sidebar-hover);
}

.nav-item.active {
  background: var(--sidebar-hover);
}

.nav-icon {
  font-size: 1.5rem;
}

.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.user-info-left {
  flex-direction: row-reverse;
  justify-content: flex-end;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-details span {
  display: block;
  font-weight: 600;
}

.user-details small {
  opacity: 0.8;
  font-size: 0.8rem;
}

.logout-btn {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-family: inherit;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* System Controls */
.system-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.control-btn.active {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.control-btn.inactive {
  background: rgba(255, 255, 255, 0.05);
  opacity: 0.6;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.main-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  min-height: 100vh;
  transition: all 0.3s ease;
}

/* Arabic - Main content with right margin for sidebar */
.lang-ar .main-content {
  margin-right: 280px;
  margin-left: 0;
}

/* French and English - Main content with left margin for sidebar */
.lang-fr .main-content,
.lang-en .main-content {
  margin-left: 280px;
  margin-right: 0;
}

/* Responsive Tables for Multi-Language Support */
.table-container {
  overflow-x: auto;
  margin: 1rem 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.responsive-table {
  width: 100%;
  min-width: 800px;
  border-collapse: collapse;
  background: white;
}

.responsive-table th,
.responsive-table td {
  padding: 12px 8px;
  border-bottom: 1px solid var(--border-color);
  white-space: nowrap;
}

.responsive-table th {
  background: var(--primary-color);
  color: white;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Arabic - Right aligned table content */
.lang-ar .responsive-table {
  direction: rtl !important;
}

.lang-ar .responsive-table th,
.lang-ar .responsive-table td {
  text-align: right !important;
}

/* French and English - Left aligned table content */
.lang-fr .responsive-table,
.lang-en .responsive-table {
  direction: ltr !important;
}

.lang-fr .responsive-table th,
.lang-fr .responsive-table td,
.lang-en .responsive-table th,
.lang-en .responsive-table td {
  text-align: left !important;
}

/* Override any existing table styles */
.lang-fr .table-container,
.lang-en .table-container {
  direction: ltr !important;
}

.lang-fr .data-table,
.lang-en .data-table {
  direction: ltr !important;
}

.lang-fr .data-table th,
.lang-fr .data-table td,
.lang-en .data-table th,
.lang-en .data-table td {
  text-align: left !important;
}

/* Mobile responsive tables */
@media (max-width: 768px) {
  .table-container {
    margin: 0 -1rem;
    border-radius: 0;
  }

  .responsive-table {
    min-width: 600px;
    font-size: 0.9rem;
  }

  .responsive-table th,
  .responsive-table td {
    padding: 8px 4px;
  }
}

/* Large screen optimization */
@media (min-width: 1400px) {
  .responsive-table {
    min-width: 100%;
  }

  .responsive-table th,
  .responsive-table td {
    padding: 16px 12px;
  }
}

/* Form and Input Multi-Language Support */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
}

/* Arabic - Right aligned form elements */
.lang-ar .form-group label {
  text-align: right;
}

.lang-ar .form-group input,
.lang-ar .form-group select,
.lang-ar .form-group textarea {
  text-align: right;
  direction: rtl;
}

/* French and English - Left aligned form elements */
.lang-fr .form-group label,
.lang-en .form-group label {
  text-align: left;
}

.lang-fr .form-group input,
.lang-fr .form-group select,
.lang-fr .form-group textarea,
.lang-en .form-group input,
.lang-en .form-group select,
.lang-en .form-group textarea {
  text-align: left;
  direction: ltr;
}

/* Button Multi-Language Support */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--sidebar-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.3);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background: #e9ecef;
}

/* Arabic - Right aligned buttons with icons on right */
.lang-ar .btn {
  flex-direction: row;
}

/* French and English - Left aligned buttons with icons on left */
.lang-fr .btn,
.lang-en .btn {
  flex-direction: row;
}

/* Quick Operations Buttons Multi-Language Support */
.quick-operations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.quick-operation-btn {
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: var(--primary-color);
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 2px 8px rgba(22, 160, 133, 0.2);
}

.quick-operation-btn:hover {
  background: var(--sidebar-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.3);
}

.quick-operation-btn .btn-icon {
  font-size: 1.2rem;
  min-width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-operation-btn .btn-text {
  flex: 1;
}

/* Arabic - Icons on the right side of buttons */
.lang-ar .quick-operation-btn {
  flex-direction: row-reverse !important;
}

.lang-ar .quick-operation-btn .btn-text {
  text-align: right !important;
}

/* French and English - Icons on the left side of buttons */
.lang-fr .quick-operation-btn,
.lang-en .quick-operation-btn {
  flex-direction: row !important;
}

.lang-fr .quick-operation-btn .btn-text,
.lang-en .quick-operation-btn .btn-text {
  text-align: left !important;
}

/* Force button structure for all languages */
body.lang-fr .quick-operation-btn,
body.lang-en .quick-operation-btn {
  flex-direction: row !important;
}

body.lang-ar .quick-operation-btn {
  flex-direction: row-reverse !important;
}



/* Right Action Button Icon Positioning */
.right-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

/* Arabic - Icons above text for right action buttons */
.lang-ar .right-action-btn {
  flex-direction: column !important;
}

/* FLOATING BUTTON POSITIONING - REMOVED - Only keeping scroll-to-top */

/* CUSTOMER OPERATIONS MODAL STYLES */

.customer-operations {
  max-width: 100%;
  padding: 20px 0;
}

.customer-info-summary {
  margin-bottom: 30px;
}

.info-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #dee2e6;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.info-card h3 {
  color: #6f42c1;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.info-item:hover {
  background: #f8f9fa;
  border-color: #6f42c1;
}

.info-item strong {
  color: #495057;
  font-weight: 600;
  margin-right: 10px;
}

.operations-section {
  margin: 30px 0;
}

.operations-section h3 {
  color: #6f42c1;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.operations-summary {
  margin-top: 30px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.summary-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.summary-card.green {
  border-left: 4px solid #28a745;
}

.summary-card.blue {
  border-left: 4px solid #007bff;
}

.summary-card.orange {
  border-left: 4px solid #fd7e14;
}

.summary-card.red {
  border-left: 4px solid #dc3545;
}

.summary-card h4 {
  color: #495057;
  font-size: 0.9rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #212529;
}

/* DELETE CONFIRMATION MODAL STYLES */

.confirmation-modal {
  max-width: 500px;
}

.confirmation-content {
  text-align: center;
  padding: 20px;
}

.warning-icon {
  font-size: 4rem;
  color: #dc3545;
  margin-bottom: 20px;
  display: block;
}

.confirmation-text {
  margin-bottom: 20px;
}

.customer-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  text-align: left;
  border: 1px solid #dee2e6;
}

.customer-details p {
  margin: 8px 0;
  font-size: 1rem;
}

.customer-details strong {
  color: #495057;
  font-weight: 600;
  display: inline-block;
  min-width: 120px;
}

.warning-message {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.warning-message p {
  color: #856404;
  font-weight: 600;
  margin: 0;
}

/* Text color utilities */
.text-success {
  color: #28a745 !important;
  font-weight: 600;
}

.text-danger {
  color: #dc3545 !important;
  font-weight: 600;
}

/* Payment Section within Customer Operations */
.payment-section {
  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 25px;
  border: 1px solid #c3e6c3;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.1);
}

.payment-section h3 {
  color: #28a745;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.payment-form-inline {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.payment-info-card {
  background: white;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.payment-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.payment-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.payment-info-item strong {
  color: #495057;
  font-weight: 600;
}

.debt-amount {
  color: #dc3545;
  font-weight: bold;
  font-size: 1.1rem;
}

.payment-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 15px;
  align-items: end;
}

.payment-input {
  padding: 10px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.payment-input:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
  outline: none;
}

.payment-select {
  padding: 10px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.payment-select:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
  outline: none;
}

.payment-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.payment-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* RTL/LTR Support for Customer Operations Modal */
.modal-content[dir="rtl"] {
  text-align: right;
}

.modal-content[dir="ltr"] {
  text-align: left;
}

.modal-content[dir="rtl"] .info-grid {
  direction: rtl;
}

.modal-content[dir="ltr"] .info-grid {
  direction: ltr;
}

.modal-content[dir="rtl"] .data-table {
  direction: rtl;
}

.modal-content[dir="ltr"] .data-table {
  direction: ltr;
}

.modal-content[dir="rtl"] .summary-cards {
  direction: rtl;
}

.modal-content[dir="ltr"] .summary-cards {
  direction: ltr;
}

/* Operation type styling */
.operation-type.invoice {
  color: #28a745;
  font-weight: 600;
}

.operation-type.payment {
  color: #007bff;
  font-weight: 600;
}

.payment-amount {
  color: #dc3545;
  font-weight: 600;
}

.status.payment {
  background: #d4edda;
  color: #155724;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
}

/* Responsive adjustments for customer operations */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .summary-cards {
    grid-template-columns: 1fr;
  }

  .customer-details {
    text-align: center;
  }

  .customer-details strong {
    display: block;
    margin-bottom: 5px;
  }

  .payment-form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .payment-info-grid {
    grid-template-columns: 1fr;
  }
}

/* PAYMENT MODAL STYLES */

.payment-modal {
  max-width: 600px;
}

.payment-content {
  padding: 20px 0;
}

.customer-payment-info {
  margin-bottom: 30px;
}

.payment-info-card {
  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #c3e6c3;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.1);
}

.payment-info-card h3 {
  color: #28a745;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.payment-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.payment-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.payment-info-item:hover {
  background: #f8f9fa;
  border-color: #28a745;
}

.payment-info-item strong {
  color: #495057;
  font-weight: 600;
  margin-right: 10px;
}

.debt-amount {
  color: #dc3545;
  font-weight: 700;
  font-size: 1.1rem;
}

.payment-amount {
  color: #28a745;
  font-weight: 700;
  font-size: 1.1rem;
}

.payment-form {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #dee2e6;
}

.payment-form h3 {
  color: #6f42c1;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.payment-help {
  color: #6c757d;
  font-size: 0.85rem;
  margin-top: 5px;
  display: block;
}

.payment-summary {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid #e9ecef;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 1rem;
}

.summary-row.total {
  font-size: 1.2rem;
  font-weight: 700;
  padding: 12px 0;
}

.summary-divider {
  height: 1px;
  background: #dee2e6;
  margin: 10px 0;
}

/* Payment modal responsive */
@media (max-width: 768px) {
  .payment-modal {
    max-width: 95%;
  }

  .payment-info-grid {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .summary-row {
    font-size: 0.9rem;
  }

  .summary-row.total {
    font-size: 1.1rem;
  }
}

/* French and English - Icons above text for right action buttons */
.lang-fr .right-action-btn,
.lang-en .right-action-btn {
  flex-direction: column !important;
}

/* Page Headers and Titles Multi-Language Support */
.page-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.page-title-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.page-title-section h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.page-title-section p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Arabic - Right aligned titles */
.lang-ar .page-header,
.lang-ar .page-title-section,
.lang-ar .page-title-section h1,
.lang-ar .page-title-section p {
  text-align: right;
}

.lang-ar .dashboard-section h2,
.lang-ar .stat-card h3,
.lang-ar .kpi-card h4 {
  text-align: right;
}

/* French and English - Left aligned titles */
.lang-fr .page-header,
.lang-fr .page-title-section,
.lang-fr .page-title-section h1,
.lang-fr .page-title-section p,
.lang-en .page-header,
.lang-en .page-title-section,
.lang-en .page-title-section h1,
.lang-en .page-title-section p {
  text-align: left;
}

.lang-fr .dashboard-section h2,
.lang-fr .stat-card h3,
.lang-fr .kpi-card h4,
.lang-en .dashboard-section h2,
.lang-en .stat-card h3,
.lang-en .kpi-card h4 {
  text-align: left;
}

/* Stats Cards Multi-Language Support */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--primary-color);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 2.5rem;
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.stat-unit {
  font-size: 1rem;
  font-weight: 400;
  color: var(--text-secondary);
}

/* Arabic - Right aligned stat cards */
.lang-ar .stat-card {
  border-right: 4px solid var(--primary-color);
  border-left: none;
  flex-direction: row-reverse;
}

.lang-ar .stat-content h3,
.lang-ar .stat-value {
  text-align: right;
}

/* French and English - Left aligned stat cards */
.lang-fr .stat-card,
.lang-en .stat-card {
  border-left: 4px solid var(--primary-color);
  border-right: none;
  flex-direction: row;
}

.lang-fr .stat-content h3,
.lang-fr .stat-value,
.lang-en .stat-content h3,
.lang-en .stat-value {
  text-align: left;
}



/* Dashboard Section */
.dashboard-section {
  background: white;
  border-radius: 12px;
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.dashboard-section h2 {
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 0.5rem;
}

/* Dashboard Recent Invoices Section Header - #177e89 Color */
.dashboard .dashboard-section h2 {
  background: linear-gradient(135deg, #177e89, #20c997);
  color: white !important;
  padding: 20px 25px;
  margin: -1.5rem -1.5rem 1.5rem -1.5rem;
  border-radius: 12px 12px 0 0;
  border-bottom: none;
  font-weight: bold;
  font-size: 1.3rem;
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  margin-bottom: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  min-width: 800px;
}

.data-table th,
.data-table td {
  padding: 1rem;
  text-align: right;
  border-bottom: 1px solid var(--border-color);
  white-space: nowrap;
}

/* Override default table alignment for French and English */
.lang-fr .data-table th,
.lang-fr .data-table td,
.lang-en .data-table th,
.lang-en .data-table td {
  text-align: left !important;
}

.data-table th {
  background: var(--background-color);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table tr:hover {
  background: rgba(22, 160, 133, 0.05);
}

/* Responsive table styles */
@media (max-width: 1024px) {
  .data-table {
    min-width: 700px;
  }

  .data-table th,
  .data-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .data-table {
    min-width: 600px;
  }

  .data-table th,
  .data-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.8rem;
  }

  /* Mobile supplier table adjustments */
  .suppliers-fixed-table .action-buttons-group {
    flex-direction: column;
    gap: 0.2rem;
  }

  .suppliers-fixed-table .action-buttons-group .btn {
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
    min-width: 24px;
    height: 24px;
  }

  .supplier-info {
    flex-direction: column;
    gap: 0.2rem;
    text-align: center;
  }

  .supplier-name {
    font-size: 0.8rem;
  }

  .credit-amount {
    font-size: 0.9rem;
  }

  .transactions-info {
    gap: 0.1rem;
  }

  .transaction-count {
    font-size: 0.9rem;
  }

  .transaction-label {
    font-size: 0.7rem;
  }

  /* Adjust column widths for mobile */
  .suppliers-fixed-table th:nth-child(1),
  .suppliers-fixed-table td:nth-child(1) { width: 25%; }
  .suppliers-fixed-table th:nth-child(2),
  .suppliers-fixed-table td:nth-child(2) { width: 20%; }
  .suppliers-fixed-table th:nth-child(3),
  .suppliers-fixed-table td:nth-child(3) { width: 20%; }
  .suppliers-fixed-table th:nth-child(4),
  .suppliers-fixed-table td:nth-child(4) { width: 35%; }
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status.paid {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

.status.partial {
  background: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
}

.status.active {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

.actions-header {
  margin-top: 2rem;
  margin-bottom: 1rem;
  text-align: center;
}

.actions-header h3 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.actions-header p {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.table-actions {
  display: flex;
  gap: 0.6rem !important;
  flex-wrap: wrap;
  justify-content: center;
  padding: 1rem !important;
  background: rgba(22, 160, 133, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(22, 160, 133, 0.1);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

/* Debug Card Styles */
.debug-card {
  border: 2px dashed #9b59b6 !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  position: relative;
  overflow: hidden;
}

/* ⌨️ Keyboard Shortcuts Display Styles */
.global-shortcuts-display {
  background: rgba(22, 160, 133, 0.05);
  border: 1px solid rgba(22, 160, 133, 0.2);
  border-radius: 12px;
  padding: 1rem;
  margin-top: 1rem;
}

.global-shortcuts-display h4 {
  margin: 0 0 0.75rem 0;
  color: var(--primary-color);
  font-size: 1rem;
  font-weight: 600;
}

.shortcuts-grid {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  font-size: 0.85rem;
  color: var(--text-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.shortcut-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.shortcut-item kbd {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 2rem;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Modal Shortcuts Display */
.modal-title-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.modal-title-section h2 {
  margin: 0;
}

.shortcuts-display {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.shortcuts-display .shortcut-item {
  font-size: 0.8rem;
  padding: 0.4rem 0.6rem;
  background: rgba(255, 255, 255, 0.9);
}

.shortcuts-display .shortcut-item kbd {
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
  min-width: 1.5rem;
}

/* LTR Modal Header Layout - Title on top, Shortcuts below */
.modal-header-ltr {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
  color: white;
  border-radius: 12px 12px 0 0;
}

/* Sales Modal Header - Match floating button color */
.sales-modal-landscape .modal-header-ltr {
  background: linear-gradient(135deg, #28a745, #20c997) !important;
}

/* Purchase Modal Header - Match floating button color */
.purchase-modal-landscape .modal-header-ltr {
  background: linear-gradient(135deg, #007bff, #6610f2) !important;
}

/* Client Modal Header - Match floating button color */
.customer-modal .modal-header-ltr {
  background: linear-gradient(135deg, #6f42c1, #e83e8c) !important;
  color: white !important;
}

.customer-modal .modal-header-ltr h2 {
  color: white !important;
}

/* Supplier Modal Header - Match floating button color */
.supplier-modal .modal-header-ltr {
  background: linear-gradient(135deg, #fd7e14, #ffc107) !important;
}

/* Product Modal Header - Match inventory page color (teal-purple gradient) */
.product-modal .modal-header-ltr {
  background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
}

/* Arabic Sales Invoice Modal Header - Green gradient with WHITE fonts */
.lang-ar .sales-modal .modal-header,
.lang-ar .sales-modal .modal-header-ltr,
.lang-ar .sales-modal-landscape .modal-header {
  background: linear-gradient(135deg, #28a745, #20c997) !important;
  color: white !important;
}

.lang-ar .sales-modal .modal-header h2,
.lang-ar .sales-modal .modal-header-ltr h2,
.lang-ar .sales-modal-landscape .modal-header h2 {
  color: white !important;
}

/* Arabic Purchase Invoice Modal Header - Blue gradient with WHITE fonts */
.lang-ar .purchase-modal .modal-header,
.lang-ar .purchase-modal .modal-header-ltr,
.lang-ar .purchase-modal-landscape .modal-header {
  background: linear-gradient(135deg, #007bff, #6610f2) !important;
  color: white !important;
}

.lang-ar .purchase-modal .modal-header h2,
.lang-ar .purchase-modal .modal-header-ltr h2,
.lang-ar .purchase-modal-landscape .modal-header h2 {
  color: white !important;
}

/* Arabic Customer Modal Header - Purple gradient with WHITE fonts */
.lang-ar .customer-modal .modal-header,
.lang-ar .customer-modal .modal-header-ltr {
  background: linear-gradient(135deg, #6f42c1, #e83e8c) !important;
  color: white !important;
}

.lang-ar .customer-modal .modal-header h2,
.lang-ar .customer-modal .modal-header-ltr h2 {
  color: white !important;
}

/* Suppliers Section Styling */
.suppliers-section {
  margin-top: 40px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  background: linear-gradient(135deg, #fd7e14, #ffc107);
  color: white;
  margin-bottom: 0;
}

.section-header-ltr {
  flex-direction: row;
}

.section-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

.suppliers-table-container {
  padding: 0;
  overflow-x: auto;
}

.suppliers-table-container .data-table {
  margin: 0;
  border-radius: 0;
  box-shadow: none;
}

/* REMOVED OLD PAGE HEADER STYLES - REPLACED WITH NEW SPECIFIC ONES BELOW */

/* NEW TABLE HEADER COLORS - MATCH PAGE HEADERS */
.dashboard .data-table thead th,
.dashboard .inventory-table thead th {
  background: linear-gradient(135deg, #177e89, #20c997);
  color: white;
}

.sales-page .data-table thead th,
.sales-page .inventory-table thead th {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.purchases-page .data-table thead th,
.purchases-page .inventory-table thead th {
  background: linear-gradient(135deg, #007bff, #6610f2);
  color: white;
}

.customers-page .data-table thead th,
.customers-page .inventory-table thead th {
  background: linear-gradient(135deg, #6f42c1, #e83e8c);
  color: white;
}

.inventory-page .data-table thead th,
.inventory-page .inventory-table thead th {
  background: linear-gradient(135deg, #17a2b8, #6f42c1);
  color: white;
}

.settings-page .data-table thead th,
.settings-page .inventory-table thead th {
  background: linear-gradient(135deg, #fd7e14, #ffc107);
  color: white;
}

.reports-page .data-table thead th,
.reports-page .inventory-table thead th {
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
  color: white;
}

/* Ensure text is white in colored headers */
.dashboard .page-header h1,
.sales-page .page-header h1,
.purchases-page .page-header h1,
.customers-page .page-header h1,
.inventory-page .page-header h1,
.settings-page .page-header h1,
.reports-page .page-header h1,
.dashboard .page-header p,
.sales-page .page-header p,
.purchases-page .page-header p,
.customers-page .page-header p,
.inventory-page .page-header p,
.settings-page .page-header p,
.reports-page .page-header p {
  color: white !important;
}

/* REMOVED OLD SPLIT LAYOUT STYLES - REPLACED WITH NEW SPECIFIC ONES BELOW */

/* Supplier Modal Responsive Design */
.supplier-modal {
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.supplier-modal .modal-header {
  background: linear-gradient(135deg, #fd7e14, #ffc107);
  color: white;
  padding: 20px 30px;
  border-radius: 15px 15px 0 0;
  border-bottom: none;
}

/* Customer Modal Header - Match customers page color */
.customer-modal .modal-header {
  background: linear-gradient(135deg, #6f42c1, #e83e8c);
  color: white;
  padding: 20px 30px;
  border-radius: 15px 15px 0 0;
  border-bottom: none;
}

.customer-modal .modal-header h2 {
  color: white !important;
}

/* Product Modal Header - Match inventory page color */
.product-modal .modal-header {
  background: linear-gradient(135deg, #17a2b8, #6f42c1);
  color: white;
  padding: 20px 30px;
  border-radius: 15px 15px 0 0;
  border-bottom: none;
}

/* Arabic Sales Invoice Modal Header - Green gradient */
.lang-ar .sales-modal-landscape .modal-header {
  background: linear-gradient(135deg, #28a745, #20c997) !important;
  color: white !important;
}

/* Arabic Purchase Invoice Modal Header - Blue gradient */
.lang-ar .purchase-modal-landscape .modal-header {
  background: linear-gradient(135deg, #007bff, #6610f2) !important;
  color: white !important;
}

.supplier-modal .modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  flex: 1;
}

.supplier-modal .modal-shortcuts {
  display: none; /* Hide shortcuts for supplier modal */
}

.supplier-modal .modal-body {
  padding: 30px;
}

.supplier-modal .form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.supplier-modal .form-group.full-width {
  grid-column: 1 / -1;
}

.supplier-modal .modal-footer {
  padding: 20px 30px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  border-radius: 0 0 15px 15px;
}

/* Responsive adjustments for supplier modal */
@media (max-width: 768px) {
  .supplier-modal {
    width: 95%;
    margin: 10px;
    max-height: 95vh;
  }

  .supplier-modal .form-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .supplier-modal .modal-header {
    padding: 15px 20px;
  }

  .supplier-modal .modal-body {
    padding: 20px;
  }

  .supplier-modal .modal-footer {
    padding: 15px 20px;
  }
}

.modal-header-ltr .modal-title-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  gap: 1rem;
}

.modal-header-ltr .modal-title-section h2 {
  margin: 0;
  text-align: left;
  white-space: nowrap;
  color: white;
  font-weight: bold;
}

.modal-header-ltr .shortcuts-display {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  margin-top: 0.5rem;
}

/* Additional styling for LTR shortcuts */
.modal-header-ltr .shortcuts-display .shortcut-item {
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
  white-space: nowrap;
}

/* Responsive behavior for smaller screens */
@media (max-width: 768px) {
  .modal-header-ltr .shortcuts-display {
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .modal-header-ltr .shortcuts-display .shortcut-item {
    font-size: 0.75rem;
  }
}

/* Page Header Layout */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.page-title-section {
  flex: 1;
}

.page-title-section h1 {
  margin: 0 0 0.5rem 0;
}

.page-title-section p {
  margin: 0;
}

/* LTR Page Header Layout - Title Left, Description Right */
.page-header-ltr {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.page-header-ltr h1 {
  margin: 0 0 0.5rem 0;
  text-align: left;
  flex: 0 0 auto;
}

/* REMOVED OLD GENERIC page-header-ltr-split STYLES - REPLACED WITH PAGE-SPECIFIC ONES */

.page-header-ltr p {
  margin: 0;
  text-align: right;
  flex: 1;
  padding-left: 2rem;
}

/* REMOVED OLD SALES PAGE HEADER STYLES - REPLACED WITH NEW SPECIFIC ONES BELOW */

/* 🔊 Sound Notification Styles */
.sound-notification {
  position: fixed;
  top: 20px;
  left: 20px;
  background: rgba(22, 160, 133, 0.95);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  z-index: 10000;
  animation: soundNotificationSlide 2s ease-out forwards;
  pointer-events: none;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

@keyframes soundNotificationSlide {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  20% {
    transform: translateX(0);
    opacity: 1;
  }
  80% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* Shortcut Feedback Styles */
.shortcut-feedback {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(22, 160, 133, 0.95);
  color: white;
  padding: 20px 30px;
  border-radius: 15px;
  font-family: 'Cairo', sans-serif;
  text-align: center;
  z-index: 10001;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: shortcutFeedback 1.5s ease-out forwards;
  pointer-events: none;
  backdrop-filter: blur(10px);
}

.shortcut-feedback .shortcut-key {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 8px;
  display: inline-block;
}

.shortcut-feedback .shortcut-desc {
  font-size: 16px;
  opacity: 0.9;
}

@keyframes shortcutFeedback {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }
  40% {
    transform: translate(-50%, -50%) scale(1);
  }
  80% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
}

/* Responsive Design for Shortcuts */
@media (max-width: 1024px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .global-shortcuts-display {
    margin-top: 0;
  }

  .shortcuts-grid {
    justify-content: center;
  }

  .shortcut-item {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }

  .shortcut-item kbd {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (max-width: 768px) {
  .global-shortcuts-display {
    padding: 0.75rem;
  }

  .global-shortcuts-display h4 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .shortcuts-grid {
    gap: 0.5rem;
  }

  .shortcut-item {
    font-size: 0.75rem;
    padding: 0.3rem 0.5rem;
  }

  .shortcut-item kbd {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
    min-width: 1.2rem;
  }

  /* Hide shortcuts display on very small screens - but not in LTR modals */
  .shortcuts-display:not(.modal-header-ltr .shortcuts-display) {
    display: none;
  }

  /* Keep LTR modal shortcuts visible */
  .modal-header-ltr .shortcuts-display {
    display: flex !important;
  }

  .modal-title-section {
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .global-shortcuts-display {
    display: none;
  }

  .system-controls {
    padding: 0.5rem;
    gap: 0.25rem;
  }

  .control-btn {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
}

.debug-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #9b59b6, #e74c3c, #f39c12, #27ae60, #3498db);
  animation: debugGlow 2s ease-in-out infinite alternate;
}

@keyframes debugGlow {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}

.debug-card .report-icon {
  color: #9b59b6;
  animation: debugPulse 1.5s ease-in-out infinite;
}

@keyframes debugPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.debug-card .report-info h3 {
  color: #9b59b6 !important;
  font-weight: 700;
}

.debug-card .report-stats span {
  color: #e74c3c !important;
  font-weight: 800;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.debug-card .report-stats small {
  color: #7f8c8d !important;
  font-style: italic;
}

.debug-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(155, 89, 182, 0.3);
  border-color: #8e44ad !important;
}

/* Category Management Styles */
.category-filter-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.category-management {
  padding: 20px;
}

.add-category-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 2px dashed #dee2e6;
}

.add-category-section h3 {
  margin-bottom: 15px;
  color: #495057;
}

.add-category-section .form-row {
  display: flex;
  gap: 10px;
  align-items: center;
}

.add-category-section input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 5px;
  font-size: 14px;
}

.categories-list h3 {
  margin-bottom: 20px;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 10px;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.category-item {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
}

.category-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.category-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  font-weight: 600;
  color: #495057;
  font-size: 16px;
}

.category-actions {
  display: flex;
  gap: 5px;
}

.edit-category input {
  width: 100%;
  padding: 8px;
  border: 2px solid #007bff;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 600;
}

/* Invoice Edit Styles */
.invoice-edit-form {
  padding: 20px;
}

.invoice-items-section {
  margin: 20px 0;
}

.invoice-items-section h3 {
  margin-bottom: 15px;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 10px;
}

.invoice-totals {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.totals-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #dee2e6;
}

.totals-row.total-final {
  font-weight: bold;
  font-size: 18px;
  color: #28a745;
  border-bottom: none;
  border-top: 2px solid #28a745;
  margin-top: 10px;
  padding-top: 15px;
}

/* Return Products Styles */
.return-form {
  padding: 20px;
}

.return-info {
  background: #e3f2fd;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #2196f3;
}

.return-info p {
  margin: 5px 0;
  color: #1565c0;
}

.return-items-section h3 {
  margin-bottom: 15px;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 10px;
}

.return-summary {
  background: #fff3cd;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
  border-left: 4px solid #ffc107;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  font-size: 16px;
  color: #856404;
}

/* Large Modal Styles */
.large-modal {
  max-width: 90vw;
  width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
}

.large-modal .modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

/* Category Input Group Styles */
.category-input-group {
  display: flex;
  gap: 5px;
  align-items: center;
}

.category-input-group select {
  flex: 1;
}

.category-input-group .btn {
  padding: 8px 12px;
  font-size: 12px;
  white-space: nowrap;
}

/* Action Buttons Group Styles */
.action-buttons-group {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
}

/* Action Buttons Row - Single Row Layout */
.action-buttons-row {
  display: flex !important;
  gap: 3px !important;
  flex-wrap: nowrap !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 180px;
  overflow-x: auto !important;
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.action-buttons-row::-webkit-scrollbar {
  display: none !important;
}

/* XS Action Buttons - Extra Small Size - Enhanced Visibility */
.action-buttons-xs .btn-xs {
  min-width: 28px !important;
  max-width: 28px !important;
  width: 28px !important;
  height: 28px !important;
  font-size: 14px !important;
  padding: 0 !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 1px solid rgba(0,0,0,0.2) !important;
  transition: all 0.2s ease !important;
  flex-shrink: 0 !important;
  background: white !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.action-buttons-xs .btn-xs:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(0,0,0,0.2) !important;
  border-color: rgba(0,0,0,0.3) !important;
}

/* Summary Row Single Layout */
.summary-row-single {
  display: flex !important;
  gap: 1rem !important;
  flex-wrap: nowrap !important;
  align-items: stretch !important;
  justify-content: space-between !important;
  width: 100% !important;
}

.summary-card.compact {
  flex: 1 !important;
  min-width: 0 !important;
  padding: 0.75rem !important;
  margin: 0 !important;
}

.summary-card.compact .card-label {
  font-size: 0.8rem !important;
  margin-bottom: 0.25rem !important;
}

.summary-card.compact .card-value {
  font-size: 1rem !important;
  font-weight: 600 !important;
}

/* Floating Action Buttons */
.floating-actions-bottom {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 1000;
}

.btn-floating {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-floating:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.btn-floating.btn-primary {
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
}

.btn-floating.btn-secondary {
  background: linear-gradient(135deg, #6c757d, #5a6268);
  color: white;
}

/* Clean Buttons */
.btn-clean {
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  color: #495057;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-clean:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-clean.btn-primary {
  background: #498C8A;
  color: white;
  border-color: #498C8A;
}

.btn-clean.btn-primary:hover {
  background: #3a7270;
  border-color: #3a7270;
}

.btn-clean.btn-secondary {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.btn-clean.btn-secondary:hover {
  background: #5a6268;
  border-color: #5a6268;
}

/* Danger Button for Delete Actions */
.btn-modern.btn-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: 1px solid #dc3545;
}

.btn-modern.btn-danger:hover {
  background: linear-gradient(135deg, #c82333, #bd2130);
  border-color: #bd2130;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.btn-modern.btn-danger:disabled {
  background: #6c757d;
  border-color: #6c757d;
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Warning Text Styling */
.warning-text {
  color: #dc3545 !important;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Warning Section */
.warning-section {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.warning-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.warning-message h3 {
  color: #dc3545;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.warning-message p {
  color: #6c757d;
  margin: 0;
  line-height: 1.5;
}

/* Summary Cards for Delete Modal */
.summary-cards {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.summary-cards .summary-card {
  flex: 1;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.summary-cards .card-label {
  font-size: 0.875rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.summary-cards .card-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #495057;
}

.action-buttons-group .btn {
  padding: 6px 10px;
  font-size: 12px;
  min-width: auto;
  white-space: nowrap;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-buttons-group .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-buttons-group .btn-sm {
  padding: 4px 8px;
  font-size: 11px;
}

.action-buttons-group .btn-xs {
  padding: 0 !important;
  font-size: 14px !important;
  min-width: 32px !important;
  max-width: 32px !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: none !important;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center !important;
  line-height: 1 !important;
}

.action-buttons-group .btn-xs:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.action-buttons-group .btn-xs:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-buttons-group {
  display: flex !important;
  gap: 3px !important;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

/* Left-align action buttons for all languages */
.action-buttons-group {
  justify-content: flex-start !important;
}

/* Force square buttons in action groups */
td .action-buttons-group .btn,
.action-buttons-group .btn.btn-xs,
.action-buttons-group button.btn {
  padding: 0 !important;
  min-width: 32px !important;
  max-width: 32px !important;
  width: 32px !important;
  height: 32px !important;
  font-size: 14px !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0 !important;
  text-align: center !important;
  line-height: 1 !important;
  border: none !important;
}

/* Customer Styles */
.customers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.customer-card {
  background: white;
  border-radius: 12px;
  box-shadow: var(--shadow);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.customer-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.customer-info h3 {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.customer-info p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.customer-balance {
  margin: 1rem 0;
  padding: 1rem;
  background: var(--background-color);
  border-radius: 8px;
  text-align: center;
}

.balance-label {
  display: block;
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.balance-amount {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--primary-color);
}

.customer-status {
  text-align: center;
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: var(--shadow);
  text-align: center;
  border-top: 4px solid;
}

.summary-card.orange { border-top-color: var(--warning-color); }
.summary-card.blue { border-top-color: var(--info-color); }
.summary-card.green { border-top-color: var(--success-color); }

.summary-card h3 {
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.summary-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
}

/* Coming Soon */
.coming-soon {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: var(--shadow);
}

.coming-soon h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.coming-soon p {
  color: var(--text-secondary);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* LTR layout for French/English - title on left, close button on right */
.modal-header-ltr {
  flex-direction: row !important;
  justify-content: space-between !important;
}

.modal-header-ltr h2 {
  order: 1;
  text-align: left !important;
  margin: 0;
  flex: 1;
}

.modal-header-ltr .modal-close {
  order: 2;
  margin-left: auto;
}

/* Ensure title is on left for French/English */
.lang-fr .modal-header,
.lang-en .modal-header {
  direction: ltr !important;
  justify-content: space-between !important;
}

.lang-fr .modal-header h2,
.lang-en .modal-header h2 {
  text-align: left !important;
  order: 1;
  flex: 1;
  margin: 0;
}

.lang-fr .modal-close,
.lang-en .modal-close {
  order: 2;
  margin-left: auto;
  margin-right: 0;
}

.lang-ar .modal-header {
  direction: rtl !important;
  justify-content: space-between !important;
}

.lang-ar .modal-header h2 {
  text-align: right !important;
  order: 1;
  flex: 1;
  margin: 0;
}

.lang-ar .modal-close {
  order: 2;
  margin-right: auto;
  margin-left: 0;
}

.modal-header h2 {
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.modal-close:hover {
  background: var(--background-color);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* LTR layout for French/English - buttons on bottom right */
.modal-footer-ltr {
  justify-content: flex-end !important;
  flex-direction: row !important;
  text-align: right !important;
}

.modal-footer-ltr .btn {
  order: initial;
}

/* Ensure buttons are on the right for French/English */
.lang-fr .modal-footer,
.lang-en .modal-footer {
  justify-content: flex-end !important;
  direction: ltr !important;
}

.lang-ar .modal-footer {
  justify-content: flex-end !important;
  direction: rtl !important;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-family: inherit;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-group input[readonly] {
  background: var(--background-color);
  color: var(--text-secondary);
}

/* Button Styles - Compact Version */
.btn {
  padding: 0.5rem 1rem !important;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  text-decoration: none;
  font-size: 0.8rem !important;
  min-width: 100px !important;
  position: relative;
  overflow: hidden;
}

/* Override for action buttons - force square shape */
.action-buttons-group .btn {
  padding: 0 !important;
  min-width: 32px !important;
  max-width: 32px !important;
  width: 32px !important;
  height: 32px !important;
  gap: 0 !important;
}

.btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover:before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--sidebar-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(22, 160, 133, 0.2);
}

.btn-secondary {
  background: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background: #475569;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3);
}

.btn-secondary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(100, 116, 139, 0.2);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-success:hover {
  background: #1e8449;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.btn-success:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(39, 174, 96, 0.2);
}

.btn-info {
  background: var(--info-color);
  color: white;
}

.btn-info:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-info:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
}

.btn-warning:hover {
  background: #d68910;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background: #c0392b;
}

/* Compact button style for headers */
.btn-compact {
  padding: 0.5rem 1rem !important;
  font-size: 0.8rem !important;
  min-width: 120px !important;
  max-width: 140px !important;
  height: 36px !important;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-actions .btn-compact {
  white-space: nowrap;
}

/* Dropdown toggle button */
.dropdown-toggle.btn-compact {
  min-width: 100px !important;
  max-width: 120px !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 250px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .login-container {
    grid-template-columns: 1fr;
  }

  .login-info {
    display: none;
  }

  .accounting-system {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    z-index: 999;
  }

  .sidebar-nav {
    display: flex;
    overflow-x: auto;
    padding: 0.5rem;
  }

  .nav-item {
    white-space: nowrap;
    min-width: 120px;
    text-align: center;
    padding: 0.75rem 1rem;
  }

  .main-content {
    padding: 1rem;
    margin-right: 0;
  }

  .scroll-to-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .customers-grid {
    grid-template-columns: 1fr;
  }

  .summary-cards {
    grid-template-columns: 1fr;
  }

  .table-actions {
    flex-direction: column;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  /* Responsive button adjustments */
  .btn-compact {
    min-width: 80px !important;
    max-width: 100px !important;
    font-size: 0.7rem !important;
    padding: 0.4rem 0.6rem !important;
  }

  .action-buttons-group .btn-xs {
    width: 28px;
    height: 28px;
    min-width: 28px;
    max-width: 28px;
    font-size: 12px;
  }
}

/* Arabic Font Styles */
.arabic {
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

.english, .french {
  font-family: 'Inter', sans-serif;
  direction: ltr;
}

/* RTL Layout Support */
body {
  direction: rtl;
}

.accounting-system {
  direction: rtl;
}

/* Sales Invoice Modal Styles */
.sales-modal {
  max-width: 1000px;
  width: 95%;
}

/* Modal Header Language Support */
.lang-fr .modal-title-section h2,
.lang-en .modal-title-section h2 {
  direction: ltr;
  text-align: left;
}

.lang-ar .modal-title-section h2 {
  direction: rtl;
  text-align: right;
}

/* Shortcut Display Language Support */
.lang-fr .shortcuts-display,
.lang-en .shortcuts-display {
  direction: ltr;
  justify-content: flex-end;
}

.lang-ar .shortcuts-display {
  direction: rtl;
  justify-content: flex-start;
}

/* Button Icon and Text Positioning */
.lang-fr .btn,
.lang-en .btn {
  flex-direction: row; /* Icon first, then text */
}

.lang-ar .btn {
  flex-direction: row-reverse; /* Text first, then icon */
}

/* Action Buttons Language Support */
.lang-fr .action-buttons,
.lang-en .action-buttons {
  direction: ltr;
  justify-content: flex-start;
}

.lang-ar .action-buttons {
  direction: rtl;
  justify-content: flex-end;
}

/* Form Labels Language Support */
.lang-fr .form-group label,
.lang-en .form-group label {
  text-align: left;
}

.lang-ar .form-group label {
  text-align: right;
}

/* Table Language Support for Sales Modal */
.lang-fr .items-table,
.lang-en .items-table {
  direction: ltr;
}

.lang-fr .items-table th,
.lang-fr .items-table td,
.lang-en .items-table th,
.lang-en .items-table td {
  text-align: left;
}

.lang-ar .items-table {
  direction: rtl;
}

.lang-ar .items-table th,
.lang-ar .items-table td {
  text-align: right;
}

/* SPECIFIC FRENCH/ENGLISH LAYOUT REQUIREMENTS */

/* 1. Modal Title - Left aligned for FR/EN */
.lang-fr .modal-title-section h2,
.lang-en .modal-title-section h2 {
  text-align: left !important;
  justify-content: flex-start !important;
  display: flex;
  align-items: center;
}

/* 2. Barcode Section - Left aligned for FR/EN */
.lang-fr .barcode-section,
.lang-en .barcode-section {
  text-align: left !important;
}

.lang-fr .barcode-section label,
.lang-en .barcode-section label {
  text-align: left !important;
  display: block;
  margin-bottom: 8px;
}

/* 3. Product Row - Right to Left for FR/EN */
.lang-fr .form-row,
.lang-en .form-row {
  direction: ltr !important;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.lang-fr .form-group,
.lang-en .form-group {
  text-align: left !important;
}

/* 4. Invoice Items Section - Left aligned for FR/EN */
.lang-fr .items-section h3,
.lang-en .items-section h3 {
  text-align: left !important;
}

.lang-fr .no-items,
.lang-en .no-items {
  text-align: left !important;
}

/* 5. Totals Summary - Right aligned for FR/EN */
.lang-fr .totals-summary,
.lang-en .totals-summary {
  text-align: right !important;
  direction: ltr !important;
}

.lang-fr .totals-summary .total-row,
.lang-en .totals-summary .total-row {
  justify-content: flex-end !important;
  text-align: right !important;
}

/* 6. Action Buttons - Right aligned for FR/EN */
.lang-fr .action-buttons,
.lang-en .action-buttons {
  justify-content: flex-end !important;
  direction: ltr !important;
  display: flex;
  gap: 10px;
}

/* 7. Discount Input - Right aligned for FR/EN */
.lang-fr .discount-input,
.lang-en .discount-input {
  text-align: right !important;
}

.lang-fr .discount-input label,
.lang-en .discount-input label {
  text-align: right !important;
}

/* 8. Additional Specific Layout Fixes for FR/EN */

/* Modal content direction for FR/EN */
.lang-fr .sales-modal-landscape,
.lang-en .sales-modal-landscape {
  direction: ltr !important;
}

/* Customer info section - left aligned for FR/EN */
.lang-fr .customer-info,
.lang-en .customer-info {
  text-align: left !important;
}

/* Product selection container - left aligned for FR/EN */
.lang-fr .product-selection-container,
.lang-en .product-selection-container {
  text-align: left !important;
}

/* Totals grid - right alignment for FR/EN */
.lang-fr .totals-grid,
.lang-en .totals-grid {
  direction: ltr !important;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

/* Invoice header section - left aligned for FR/EN */
.lang-fr .invoice-header-section,
.lang-en .invoice-header-section {
  text-align: left !important;
  direction: ltr !important;
}

/* Product selection section - left aligned for FR/EN */
.lang-fr .product-selection-section,
.lang-en .product-selection-section {
  text-align: left !important;
  direction: ltr !important;
}

/* Invoice content section - mixed alignment for FR/EN */
.lang-fr .invoice-content-section,
.lang-en .invoice-content-section {
  direction: ltr !important;
}

/* PURCHASE MANAGEMENT LAYOUT FOR FR/EN - SIMPLIFIED */

/* Purchase page header - CORRECT LAYOUT: Title LEFT, Button RIGHT */
.lang-fr .page-header,
.lang-en .page-header {
  direction: ltr !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.lang-fr .page-header h1,
.lang-en .page-header h1 {
  text-align: left !important;
  order: 1 !important;
  margin: 0 !important;
  flex: 1 !important;
}

.lang-fr .page-header button,
.lang-en .page-header button {
  order: 2 !important;
  margin-left: auto !important;
  margin-right: 0 !important;
  flex-shrink: 0 !important;
}

/* PURCHASE INVOICE MODAL LAYOUT FOR FR/EN - COMPLETE RESPONSIVE LAYOUT */

/* 1. Modal title - LEFT aligned for FR/EN - FORCE LEFT ALIGNMENT */
.lang-fr .purchase-modal-landscape .modal-title-section,
.lang-en .purchase-modal-landscape .modal-title-section {
  text-align: left !important;
  direction: ltr !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
}

.lang-fr .purchase-modal-landscape .modal-title-section h2,
.lang-en .purchase-modal-landscape .modal-title-section h2 {
  text-align: left !important;
  justify-content: flex-start !important;
  align-self: flex-start !important;
  margin: 0 !important;
  width: 100% !important;
  display: flex !important;
  justify-content: flex-start !important;
}

.lang-fr .purchase-modal-landscape .shortcuts-display,
.lang-en .purchase-modal-landscape .shortcuts-display {
  text-align: left !important;
  justify-content: flex-start !important;
  align-self: flex-start !important;
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 1rem !important;
}

/* Force modal header to be LTR and left-aligned */
.lang-fr .purchase-modal-landscape .modal-header,
.lang-en .purchase-modal-landscape .modal-header {
  direction: ltr !important;
  text-align: left !important;
}

/* Ensure the entire modal content follows LTR */
.lang-fr .purchase-modal-landscape,
.lang-en .purchase-modal-landscape {
  direction: ltr !important;
}

.lang-fr .purchase-modal-landscape .modal-content,
.lang-en .purchase-modal-landscape .modal-content {
  direction: ltr !important;
  text-align: left !important;
}

/* 2. Invoice header section - Responsive form grid */
.lang-fr .purchase-modal-landscape .invoice-header-section,
.lang-en .purchase-modal-landscape .invoice-header-section {
  direction: ltr !important;
  margin-bottom: 1.5rem !important;
}

.lang-fr .purchase-modal-landscape .invoice-info-grid,
.lang-en .purchase-modal-landscape .invoice-info-grid {
  direction: ltr !important;
}

.lang-fr .purchase-modal-landscape .invoice-info-grid .form-row,
.lang-en .purchase-modal-landscape .invoice-info-grid .form-row {
  display: grid !important;
  grid-template-columns: 1fr 1fr 1fr 1fr !important;
  gap: 1rem !important;
  margin-bottom: 1rem !important;
  direction: ltr !important;
}

.lang-fr .purchase-modal-landscape .form-group,
.lang-en .purchase-modal-landscape .form-group {
  margin-bottom: 0.5rem !important;
  direction: ltr !important;
}

.lang-fr .purchase-modal-landscape .form-group label,
.lang-en .purchase-modal-landscape .form-group label {
  text-align: left !important;
  display: block !important;
  margin-bottom: 0.25rem !important;
}

/* 3. Product selection section - Responsive */
.lang-fr .purchase-modal-landscape .product-selection-section,
.lang-en .purchase-modal-landscape .product-selection-section {
  direction: ltr !important;
  margin-bottom: 1.5rem !important;
}

.lang-fr .purchase-modal-landscape .product-selection-section .form-row,
.lang-en .purchase-modal-landscape .product-selection-section .form-row {
  display: grid !important;
  grid-template-columns: 2fr 1fr 1fr auto !important;
  gap: 1rem !important;
  align-items: end !important;
  direction: ltr !important;
}

/* 4. Invoice content section - Side by side layout */
.lang-fr .purchase-modal-landscape .invoice-content-section,
.lang-en .purchase-modal-landscape .invoice-content-section {
  display: grid !important;
  grid-template-columns: 2fr 1fr !important;
  gap: 2rem !important;
  direction: ltr !important;
  align-items: start !important;
}

/* 5. Items section - Left side */
.lang-fr .purchase-modal-landscape .items-section,
.lang-en .purchase-modal-landscape .items-section {
  direction: ltr !important;
}

.lang-fr .purchase-modal-landscape .items-section h3,
.lang-en .purchase-modal-landscape .items-section h3 {
  text-align: left !important;
  margin-bottom: 1rem !important;
}

.lang-fr .purchase-modal-landscape .items-table,
.lang-en .purchase-modal-landscape .items-table {
  direction: ltr !important;
}

.lang-fr .purchase-modal-landscape .items-table table,
.lang-en .purchase-modal-landscape .items-table table {
  width: 100% !important;
  direction: ltr !important;
}

.lang-fr .purchase-modal-landscape .items-table th,
.lang-fr .purchase-modal-landscape .items-table td,
.lang-en .purchase-modal-landscape .items-table th,
.lang-en .purchase-modal-landscape .items-table td {
  text-align: left !important;
}

/* 6. Totals section - Right side */
.lang-fr .purchase-modal-landscape .totals-section,
.lang-en .purchase-modal-landscape .totals-section {
  direction: ltr !important;
  min-width: 300px !important;
}

.lang-fr .purchase-modal-landscape .totals-grid,
.lang-en .purchase-modal-landscape .totals-grid {
  display: flex !important;
  flex-direction: column !important;
  gap: 1rem !important;
  direction: ltr !important;
}

.lang-fr .purchase-modal-landscape .discount-input,
.lang-en .purchase-modal-landscape .discount-input {
  direction: ltr !important;
}

.lang-fr .purchase-modal-landscape .discount-input label,
.lang-en .purchase-modal-landscape .discount-input label {
  text-align: left !important;
  display: block !important;
  margin-bottom: 0.5rem !important;
}

.lang-fr .purchase-modal-landscape .totals-summary,
.lang-en .purchase-modal-landscape .totals-summary {
  direction: ltr !important;
}

.lang-fr .purchase-modal-landscape .total-row,
.lang-en .purchase-modal-landscape .total-row {
  display: flex !important;
  justify-content: space-between !important;
  direction: ltr !important;
  margin-bottom: 0.5rem !important;
}

.lang-fr .purchase-modal-landscape .action-buttons,
.lang-en .purchase-modal-landscape .action-buttons {
  display: flex !important;
  gap: 1rem !important;
  direction: ltr !important;
  margin-top: 1rem !important;
}

/* 7. Responsive breakpoints */
@media (max-width: 1200px) {
  .lang-fr .purchase-modal-landscape .invoice-content-section,
  .lang-en .purchase-modal-landscape .invoice-content-section {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .lang-fr .purchase-modal-landscape .invoice-info-grid .form-row,
  .lang-en .purchase-modal-landscape .invoice-info-grid .form-row {
    grid-template-columns: 1fr 1fr !important;
  }

  .lang-fr .purchase-modal-landscape .product-selection-section .form-row,
  .lang-en .purchase-modal-landscape .product-selection-section .form-row {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 768px) {
  .lang-fr .purchase-modal-landscape .invoice-info-grid .form-row,
  .lang-en .purchase-modal-landscape .invoice-info-grid .form-row {
    grid-template-columns: 1fr !important;
  }
}

/* FLOATING PURCHASE SUMMARY CARDS - BOTTOM POSITIONED FOR ALL LANGUAGES */

/* Base purchase summary cards - positioned under title */
.purchase-summary {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: 1.5rem !important;
  margin: 2rem 0 !important;
  padding: 0 !important;
}

/* Individual summary cards */
.purchase-summary .summary-card {
  background: white !important;
  padding: 2rem !important;
  border-radius: 12px !important;
  text-align: center !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e9ecef !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.purchase-summary .summary-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
}

/* Card titles */
.purchase-summary .summary-card h3 {
  font-size: 0.95rem !important;
  margin-bottom: 1rem !important;
  color: #6c757d !important;
  font-weight: 600 !important;
  text-align: center !important;
}

/* Card values */
.purchase-summary .summary-card .summary-value {
  font-size: 1.8rem !important;
  font-weight: 700 !important;
  color: #212529 !important;
  text-align: center !important;
}

/* Language-specific directions */
.lang-ar .purchase-summary {
  direction: rtl !important;
}

.lang-fr .purchase-summary,
.lang-en .purchase-summary {
  direction: ltr !important;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .purchase-summary {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 1rem !important;
  }

  .purchase-summary .summary-card {
    padding: 1.5rem !important;
  }

  .purchase-summary .summary-card h3 {
    font-size: 0.85rem !important;
  }

  .purchase-summary .summary-card .summary-value {
    font-size: 1.5rem !important;
  }
}

@media (max-width: 480px) {
  .purchase-summary {
    grid-template-columns: 1fr 1fr !important;
    gap: 0.75rem !important;
  }

  .purchase-summary .summary-card {
    padding: 1rem !important;
  }
}

/* Purchase Summary Card Colors - Clear and Readable */
.purchase-summary .summary-card.orange {
  border-top: 4px solid #fd7e14 !important;
  background: white !important;
}

.purchase-summary .summary-card.orange h3 {
  color: #6c757d !important;
}

.purchase-summary .summary-card.orange .summary-value {
  color: #fd7e14 !important;
}

.purchase-summary .summary-card.blue {
  border-top: 4px solid #007bff !important;
  background: white !important;
}

.purchase-summary .summary-card.blue h3 {
  color: #6c757d !important;
}

.purchase-summary .summary-card.blue .summary-value {
  color: #007bff !important;
}

.purchase-summary .summary-card.green {
  border-top: 4px solid #28a745 !important;
  background: white !important;
}

.purchase-summary .summary-card.green h3 {
  color: #6c757d !important;
}

.purchase-summary .summary-card.green .summary-value {
  color: #28a745 !important;
}

.purchase-summary .summary-card.purple {
  border-top: 4px solid #6f42c1 !important;
  background: white !important;
}

.purchase-summary .summary-card.purple h3 {
  color: #6c757d !important;
}

.purchase-summary .summary-card.purple .summary-value {
  color: #6f42c1 !important;
}

/* Purchase table - LTR direction for FR/EN */
.lang-fr .table-container,
.lang-en .table-container {
  direction: ltr !important;
}

.lang-fr .data-table,
.lang-en .data-table {
  direction: ltr !important;
  width: 100% !important;
}

.lang-fr .data-table th,
.lang-fr .data-table td,
.lang-en .data-table th,
.lang-en .data-table td {
  text-align: left !important;
}

/* Purchase action buttons - left aligned icons within Actions column for FR/EN */
.lang-fr .purchases-page .action-buttons-group,
.lang-en .purchases-page .action-buttons-group {
  justify-content: flex-start !important;
  direction: ltr !important;
  display: flex !important;
  gap: 5px !important;
}

/* General action buttons - right aligned for FR/EN (default) */
.lang-fr .action-buttons-group,
.lang-en .action-buttons-group {
  justify-content: flex-end !important;
  direction: ltr !important;
  display: flex !important;
  gap: 5px !important;
}

/* Purchase page overall direction */
.lang-fr .purchases-page,
.lang-en .purchases-page {
  direction: ltr !important;
}

/* Arabic RTL layout for purchases */
.lang-ar .purchases-page {
  direction: rtl !important;
}

.lang-ar .purchases-page .page-header {
  direction: rtl !important;
  display: flex !important;
  justify-content: space-between !important;
}

.lang-ar .purchases-page .page-header h1 {
  text-align: right !important;
}

.lang-ar .purchases-page .page-header button {
  margin-left: auto !important;
}

.lang-ar .purchases-page .data-table {
  direction: rtl !important;
}

.lang-ar .purchases-page .data-table th,
.lang-ar .purchases-page .data-table td {
  text-align: right !important;
}

.invoice-header {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.product-selection {
  margin-bottom: 20px;
  padding: 15px;
  border: 2px solid var(--primary-color);
  border-radius: 8px;
  background: rgba(22, 160, 133, 0.02);
}

.product-selection h3 {
  margin: 0 0 15px 0;
  color: var(--primary-color);
  font-size: 16px;
  font-weight: 600;
}

.invoice-items {
  margin-bottom: 20px;
}

.invoice-items h3 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 8px;
}

.no-items {
  text-align: center;
  color: var(--text-secondary);
  padding: 30px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 0;
  border: 2px dashed var(--border-color);
}

.invoice-totals {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.totals-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.total-row.final-total {
  border-top: 2px solid var(--primary-color);
  padding-top: 15px;
  margin-top: 10px;
  font-weight: bold;
  font-size: 18px;
  color: var(--primary-color);
}

.discount-input {
  width: 120px;
  padding: 6px 10px;
  border: 2px solid var(--border-color);
  border-radius: 6px;
  text-align: right;
  font-weight: 600;
  transition: border-color 0.3s ease;
}

.discount-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.readonly-input {
  background: #f8f9fa !important;
  color: var(--text-secondary) !important;
  cursor: not-allowed;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
  min-width: auto;
  border-radius: 4px;
}

/* Enhanced table styles for sales invoice */
.sales-modal .data-table th {
  background: var(--primary-color);
  color: white;
  font-weight: 600;
}

.sales-modal .data-table tr:hover {
  background: rgba(22, 160, 133, 0.08);
}

/* Product selection enhancements */
.product-selection .form-grid {
  align-items: end;
}

.product-selection .btn {
  margin-top: 8px;
}

/* Responsive adjustments for sales modal */
@media (max-width: 768px) {
  .sales-modal {
    width: 98%;
    margin: 1rem 0;
  }

  .product-selection .form-grid {
    grid-template-columns: 1fr;
    align-items: stretch;
  }

  .totals-grid {
    gap: 8px;
  }

  .total-row {
    font-size: 13px;
  }

  .total-row.final-total {
    font-size: 16px;
  }
}

/* Barcode Scanner Styles */
.barcode-section {
  margin-bottom: 15px;
  padding: 15px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border-radius: 8px;
  border-left: 4px solid #28a745;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: relative;
}

.barcode-section::before {
  content: "📷 قارئ الباركود نشط";
  position: absolute;
  top: -10px;
  right: 20px;
  background: #28a745;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.barcode-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #28a745;
  border-radius: 8px;
  font-size: 16px;
  text-align: center;
  background: white;
  transition: all 0.3s ease;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  position: relative;
}

.barcode-input:focus {
  outline: none;
  border-color: #20c997;
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
  transform: scale(1.02);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25); }
  50% { box-shadow: 0 0 0 6px rgba(40, 167, 69, 0.15); }
  100% { box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25); }
}

.barcode-input::placeholder {
  color: #6c757d;
  font-style: italic;
}

/* Inventory Page Styles */
.inventory-page {
  padding: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.inventory-controls {
  margin: 20px 0;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-section {
  display: flex;
  gap: 15px;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.filter-select {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  min-width: 150px;
}

.inventory-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow-x: auto;
  overflow-y: visible;
  margin: 20px 0;
  max-width: 100%;
  -webkit-overflow-scrolling: touch;
}

.inventory-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
  table-layout: auto;
}

.inventory-table th,
.inventory-table td {
  padding: 12px 8px;
  text-align: right;
  border-bottom: 1px solid #eee;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.inventory-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
  position: sticky;
  top: 0;
  z-index: 5;
}

.inventory-table tr:hover {
  background: #f8f9fa;
}

.inventory-table tr.low-stock {
  background: #fff3cd;
}

/* Clickable row styling */
.inventory-table tr.clickable-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.inventory-table tr.clickable-row:hover {
  background: #e3f2fd !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.inventory-table tr.clickable-row:active {
  transform: translateY(0);
}

/* Barcode Display Styling */
.barcode-display {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  font-weight: 600;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  color: #495057;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.barcode-display:empty::before {
  content: "غير محدد";
  color: #6c757d;
  font-style: italic;
  font-family: inherit;
}

/* Responsive barcode styling */
@media (max-width: 768px) {
  .barcode-display {
    font-size: 10px;
    padding: 2px 4px;
    min-width: 60px;
  }
}

@media (max-width: 480px) {
  .barcode-display {
    font-size: 9px;
    padding: 1px 2px;
    min-width: 50px;
  }
}

/* Mobile Cards Container - Hidden by default */
.mobile-cards-container {
  display: none;
}

.stock-input {
  width: 80px;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
}

.stock-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.stock-status.low {
  background: #f8d7da;
  color: #721c24;
}

.stock-status.normal {
  background: #d4edda;
  color: #155724;
}

.stock-status.high {
  background: #cce5ff;
  color: #004085;
}

/* Barcode display styling */
.barcode-display {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  color: #495057;
  font-weight: 600;
  letter-spacing: 1px;
}

/* Dropdown menu styling */
.dropdown-container {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  position: relative;
}

.dropdown-toggle::after {
  content: '▼';
  margin-left: 8px;
  font-size: 10px;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 200px;
  padding: 8px 0;
  margin-top: 4px;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item.danger {
  color: #dc3545;
}

.dropdown-item.danger:hover {
  background-color: #f8d7da;
}

.action-buttons {
  display: flex;
  gap: 5px;
  justify-content: center;
}

/* Left-align action buttons for all languages */
.action-buttons {
  justify-content: flex-start !important;
}

.btn-icon {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.btn-icon:hover {
  background: #f8f9fa;
}

.btn-icon.edit:hover {
  background: #e3f2fd;
}

.btn-icon.delete:hover {
  background: #ffebee;
}

.inventory-summary {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  gap: 20px;
  z-index: 1000;
  pointer-events: none;
}

.inventory-summary .summary-card {
  pointer-events: auto;
  min-width: 200px;
  max-width: 250px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  transform: translateY(100%);
  animation: slideInFromBottom 0.6s ease-out forwards;
}

.inventory-summary .summary-card:nth-child(1) {
  animation-delay: 0.1s;
}

.inventory-summary .summary-card:nth-child(2) {
  animation-delay: 0.2s;
}

.inventory-summary .summary-card:nth-child(3) {
  animation-delay: 0.3s;
}

.inventory-summary .summary-card:nth-child(4) {
  animation-delay: 0.4s;
}

.inventory-summary .summary-card:hover {
  transform: translateY(0) scale(1.05);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Enhanced floating summary cards */
.inventory-summary .summary-card h3 {
  font-size: 14px !important;
  margin: 0 0 8px 0 !important;
  color: #666 !important;
  font-weight: 600;
}

.inventory-summary .summary-card .summary-value {
  font-size: 20px !important;
  font-weight: bold !important;
  margin: 0 !important;
  color: #333 !important;
}

.inventory-summary .summary-card small {
  display: block;
  margin-top: 4px;
  font-size: 11px;
  opacity: 0.8;
}

/* Specific colors for floating cards */
.inventory-summary .summary-card.blue {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.9), rgba(41, 128, 185, 0.9)) !important;
  color: white !important;
}

.inventory-summary .summary-card.blue h3,
.inventory-summary .summary-card.blue .summary-value,
.inventory-summary .summary-card.blue small {
  color: white !important;
}

.inventory-summary .summary-card.green {
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.9), rgba(39, 174, 96, 0.9)) !important;
  color: white !important;
}

.inventory-summary .summary-card.green h3,
.inventory-summary .summary-card.green .summary-value,
.inventory-summary .summary-card.green small {
  color: white !important;
}

.inventory-summary .summary-card.orange {
  background: linear-gradient(135deg, rgba(230, 126, 34, 0.9), rgba(211, 84, 0, 0.9)) !important;
  color: white !important;
}

.inventory-summary .summary-card.orange h3,
.inventory-summary .summary-card.orange .summary-value,
.inventory-summary .summary-card.orange small {
  color: white !important;
}

.inventory-summary .summary-card.red {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.9), rgba(192, 57, 43, 0.9)) !important;
  color: white !important;
}

.inventory-summary .summary-card.red h3,
.inventory-summary .summary-card.red .summary-value,
.inventory-summary .summary-card.red small {
  color: white !important;
}



/* Navigation button animations removed - no longer needed */

.left-nav-btn:nth-child(1) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation-delay: 0.1s;
}

.left-nav-btn:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  animation-delay: 0.2s;
}

.left-nav-btn:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  animation-delay: 0.3s;
}

/* Arabic - Left nav button hover effects */
.lang-ar .left-nav-btn:hover {
  transform: translateX(0) scale(1.1);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
}

.lang-ar .left-nav-btn:active {
  transform: translateX(0) scale(0.95);
}

/* French and English - Left nav button hover effects */
.lang-fr .left-nav-btn:hover,
.lang-en .left-nav-btn:hover {
  transform: translateX(0) scale(1.1);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
}

.lang-fr .left-nav-btn:active,
.lang-en .left-nav-btn:active {
  transform: translateX(0) scale(0.95);
}





/* NEW INVENTORY MANAGEMENT PAGE LAYOUT - FR/EN: TITLE LEFT, BUTTONS RIGHT | AR: TITLE RIGHT, BUTTONS LEFT */

/* Force page header layout for all languages - Higher specificity */
.inventory-page .page-header.page-header-ltr-split,
.inventory-page .page-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 30px !important;
  padding: 30px !important;
  background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
  color: white !important;
  border-radius: 15px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Arabic - Title RIGHT, buttons LEFT */
.lang-ar .inventory-page .page-header.page-header-ltr-split,
.lang-ar .inventory-page .page-header {
  flex-direction: row-reverse !important;
}

.lang-ar .inventory-page .page-title-section h1 {
  text-align: right !important;
}

.lang-ar .inventory-page .page-description-section {
  text-align: left !important;
  order: 1 !important;
}

.lang-ar .inventory-page .page-title-section {
  order: 2 !important;
  flex: 1 !important;
}

/* French and English - Title LEFT, buttons RIGHT */
.lang-fr .inventory-page .page-header.page-header-ltr-split,
.lang-en .inventory-page .page-header.page-header-ltr-split,
.lang-fr .inventory-page .page-header,
.lang-en .inventory-page .page-header {
  flex-direction: row !important;
}

/* Ensure title is left-aligned for FR/EN */
.lang-fr .inventory-page .page-title-section h1,
.lang-en .inventory-page .page-title-section h1 {
  text-align: left !important;
}

/* Ensure buttons are right-aligned for FR/EN */
.lang-fr .inventory-page .page-description-section,
.lang-en .inventory-page .page-description-section {
  text-align: right !important;
  order: 2 !important;
}

.lang-fr .inventory-page .page-title-section,
.lang-en .inventory-page .page-title-section {
  order: 1 !important;
  flex: 1 !important;
}

/* NEW CUSTOMERS MANAGEMENT PAGE LAYOUT - FR/EN: TITLE LEFT, BUTTON RIGHT | AR: TITLE RIGHT, BUTTON LEFT */

/* Force page header layout for all languages - Higher specificity */
.customers-page .page-header.page-header-ltr-split,
.customers-page .page-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 30px !important;
  padding: 30px !important;
  background: linear-gradient(135deg, #6f42c1, #e83e8c) !important;
  color: white !important;
  border-radius: 15px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Arabic - Title RIGHT, button LEFT */
.lang-ar .customers-page .page-header.page-header-ltr-split,
.lang-ar .customers-page .page-header {
  flex-direction: row-reverse !important;
}

.lang-ar .customers-page .page-title-section h1 {
  text-align: right !important;
}

.lang-ar .customers-page .page-description-section {
  text-align: left !important;
  order: 1 !important;
}

.lang-ar .customers-page .page-title-section {
  order: 2 !important;
  flex: 1 !important;
}

/* French and English - Title LEFT, button RIGHT */
.lang-fr .customers-page .page-header.page-header-ltr-split,
.lang-en .customers-page .page-header.page-header-ltr-split,
.lang-fr .customers-page .page-header,
.lang-en .customers-page .page-header {
  flex-direction: row !important;
}

/* Ensure title is left-aligned for FR/EN */
.lang-fr .customers-page .page-title-section h1,
.lang-en .customers-page .page-title-section h1 {
  text-align: left !important;
}

/* Ensure button is right-aligned for FR/EN */
.lang-fr .customers-page .page-description-section,
.lang-en .customers-page .page-description-section {
  text-align: right !important;
  order: 2 !important;
}

.lang-fr .customers-page .page-title-section,
.lang-en .customers-page .page-title-section {
  order: 1 !important;
  flex: 1 !important;
}

/* NEW SALES MANAGEMENT PAGE LAYOUT - FR/EN: TITLE LEFT, BUTTONS RIGHT | AR: TITLE RIGHT, BUTTONS LEFT */

/* Force page header layout for all languages - Higher specificity */
.sales-page .page-header.page-header-ltr-split,
.sales-page .page-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 30px !important;
  padding: 30px !important;
  background: linear-gradient(135deg, #28a745, #20c997) !important;
  color: white !important;
  border-radius: 15px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Arabic - Title RIGHT, buttons LEFT */
.lang-ar .sales-page .page-header.page-header-ltr-split,
.lang-ar .sales-page .page-header {
  flex-direction: row-reverse !important;
}

.lang-ar .sales-page .page-title-section h1 {
  text-align: right !important;
}

.lang-ar .sales-page .page-description-section {
  text-align: left !important;
  order: 1 !important;
}

.lang-ar .sales-page .page-title-section {
  order: 2 !important;
  flex: 1 !important;
}

/* French and English - Title LEFT, buttons RIGHT */
.lang-fr .sales-page .page-header.page-header-ltr-split,
.lang-en .sales-page .page-header.page-header-ltr-split,
.lang-fr .sales-page .page-header,
.lang-en .sales-page .page-header {
  justify-content: space-between !important;
  flex-direction: row !important;
}

/* Ensure title is left-aligned for FR/EN */
.lang-fr .sales-page .page-title-section h1,
.lang-en .sales-page .page-title-section h1 {
  text-align: left !important;
}

/* Ensure buttons are right-aligned for FR/EN */
.lang-fr .sales-page .page-description-section,
.lang-en .sales-page .page-description-section {
  text-align: right !important;
  order: 2 !important;
}

.lang-fr .sales-page .page-title-section,
.lang-en .sales-page .page-title-section {
  order: 1 !important;
  flex: 1 !important;
}

/* NEW REPORTS PAGE LAYOUT - OVERRIDE page-header-ltr-split */

/* Force page header layout for all languages - Higher specificity */
.reports-page .page-header.page-header-ltr-split,
.reports-page .page-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 30px !important;
  padding: 30px !important;
  background: linear-gradient(135deg, #8e44ad, #9b59b6) !important;
  color: white !important;
  border-radius: 15px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Arabic - Title LEFT, description RIGHT */
.lang-ar .reports-page .page-header.page-header-ltr-split,
.lang-ar .reports-page .page-header {
  flex-direction: row !important;
}

.lang-ar .reports-page .page-title-section {
  order: 1 !important;
  text-align: left !important;
  flex: 0 0 auto !important;
}

.lang-ar .reports-page .page-description-section {
  order: 2 !important;
  text-align: right !important;
  flex: 0 0 auto !important;
}

/* French and English - Title left, description right */
.lang-fr .reports-page .page-header.page-header-ltr-split,
.lang-en .reports-page .page-header.page-header-ltr-split,
.lang-fr .reports-page .page-header,
.lang-en .reports-page .page-header {
  flex-direction: row !important;
}

.lang-fr .reports-page .page-title-section,
.lang-en .reports-page .page-title-section {
  order: 1 !important;
  text-align: left !important;
  flex: 0 0 auto !important;
}

.lang-fr .reports-page .page-description-section,
.lang-en .reports-page .page-description-section {
  order: 2 !important;
  flex: 0 0 auto !important;
}

/* NEW DASHBOARD PAGE LAYOUT - ENSURE #177e89 HEADER */

/* Force page header layout for all languages - Higher specificity */
.dashboard .page-header.page-header-ltr-split,
.dashboard .page-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 30px !important;
  padding: 30px !important;
  background: linear-gradient(135deg, #177e89, #20c997) !important;
  color: white !important;
  border-radius: 15px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* NEW PURCHASES PAGE LAYOUT - ENSURE BLUE HEADER */

/* Force page header layout for all languages - Higher specificity */
.purchases-page .page-header.page-header-ltr-split,
.purchases-page .page-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 30px !important;
  padding: 30px !important;
  background: linear-gradient(135deg, #007bff, #6610f2) !important;
  color: white !important;
  border-radius: 15px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* NEW SETTINGS PAGE LAYOUT - ORANGE HEADER FOR FR/EN */

/* Force page header layout for all languages - Higher specificity */
.settings-page .page-header.page-header-ltr-split,
.settings-page .page-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 30px !important;
  padding: 30px !important;
  background: linear-gradient(135deg, #fd7e14, #ffc107) !important;
  color: white !important;
  border-radius: 15px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Page Title Positioning - Updated for new layout */
.lang-fr .inventory-page .page-header h1,
.lang-en .inventory-page .page-header h1 {
  text-align: left !important;
  margin: 0 !important;
  flex: 0 0 auto !important;
}

.lang-ar .inventory-page .page-header h1 {
  text-align: right !important;
  margin: 0 !important;
  flex: 0 0 auto !important;
}

/* Header Actions Positioning */
.lang-fr .inventory-page .header-actions,
.lang-en .inventory-page .header-actions {
  display: flex !important;
  gap: 10px !important;
  align-items: center !important;
  margin: 0 !important;
  flex: 0 0 auto !important;
}

.lang-ar .inventory-page .header-actions {
  display: flex !important;
  gap: 10px !important;
  align-items: center !important;
  margin: 0 !important;
  flex: 0 0 auto !important;
}

/* Inventory Controls Layout */
.lang-fr .inventory-page .inventory-controls,
.lang-en .inventory-page .inventory-controls {
  direction: ltr !important;
}

.lang-ar .inventory-page .inventory-controls {
  direction: rtl !important;
}

/* Search Section Layout */
.lang-fr .inventory-page .search-section,
.lang-en .inventory-page .search-section {
  flex-direction: row !important;
}

.lang-ar .inventory-page .search-section {
  flex-direction: row-reverse !important;
}

/* Table Container Layout */
.lang-fr .inventory-page .inventory-table-container,
.lang-en .inventory-page .inventory-table-container {
  direction: ltr !important;
}

.lang-ar .inventory-page .inventory-table-container {
  direction: rtl !important;
}

/* Table Headers and Content Alignment */
.lang-fr .inventory-table th,
.lang-fr .inventory-table td,
.lang-en .inventory-table th,
.lang-en .inventory-table td {
  text-align: left !important;
  direction: ltr !important;
}

.lang-ar .inventory-table th,
.lang-ar .inventory-table td {
  text-align: right !important;
  direction: rtl !important;
}

/* Table Column Order for FR/EN */
.lang-fr .inventory-table,
.lang-en .inventory-table {
  direction: ltr !important;
}

.lang-ar .inventory-table {
  direction: rtl !important;
}

/* Action Buttons in Table */
.lang-fr .inventory-table .action-buttons-group,
.lang-en .inventory-table .action-buttons-group {
  justify-content: flex-start !important;
  direction: ltr !important;
}

.lang-ar .inventory-table .action-buttons-group {
  justify-content: flex-end !important;
  direction: rtl !important;
}

/* Stock Input and Display */
.lang-fr .inventory-table .stock-input,
.lang-fr .inventory-table .stock-display-vendor,
.lang-en .inventory-table .stock-input,
.lang-en .inventory-table .stock-display-vendor {
  text-align: left !important;
  direction: ltr !important;
}

.lang-ar .inventory-table .stock-input,
.lang-ar .inventory-table .stock-display-vendor {
  text-align: right !important;
  direction: rtl !important;
}

/* Barcode Display in Table */
.lang-fr .inventory-table .barcode-display,
.lang-en .inventory-table .barcode-display {
  text-align: left !important;
  direction: ltr !important;
}

.lang-ar .inventory-table .barcode-display {
  text-align: center !important;
  direction: rtl !important;
}

/* Summary Cards Content Alignment */
.lang-fr .inventory-summary .summary-card h3,
.lang-fr .inventory-summary .summary-value,
.lang-en .inventory-summary .summary-card h3,
.lang-en .inventory-summary .summary-value {
  text-align: center;
}

.lang-ar .inventory-summary .summary-card h3,
.lang-ar .inventory-summary .summary-value {
  text-align: center;
}

/* Modal Header Layout - Force flex layout */
.modal-header {
  display: flex !important;
  align-items: center !important;
  padding: 20px !important;
  border-bottom: 1px solid #dee2e6 !important;
}

/* Modal Header for French/English - Title right, close button left */
body.lang-fr .modal-header,
body.lang-en .modal-header,
.lang-fr .modal-header,
.lang-en .modal-header {
  justify-content: space-between !important;
  flex-direction: row-reverse !important;
}

/* Modal Header for Arabic - Normal order */
body.lang-ar .modal-header,
.lang-ar .modal-header {
  justify-content: space-between !important;
  flex-direction: row !important;
}

/* Modal Title Positioning */
body.lang-fr .modal-header h2,
body.lang-en .modal-header h2,
.lang-fr .modal-header h2,
.lang-en .modal-header h2 {
  text-align: right !important;
  margin: 0 !important;
  flex: 1 !important;
}

body.lang-ar .modal-header h2,
.lang-ar .modal-header h2 {
  text-align: right !important;
  margin: 0 !important;
  flex: 1 !important;
}

/* Modal Close Button */
.modal-close {
  background: none !important;
  border: none !important;
  font-size: 24px !important;
  cursor: pointer !important;
  padding: 0 !important;
  width: 30px !important;
  height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Modal Footer Layout - Force flex layout */
.modal-footer {
  display: flex !important;
  padding: 20px !important;
  border-top: 1px solid #dee2e6 !important;
  gap: 10px !important;
}

/* Modal Footer Button Positioning - Buttons on left for FR/EN */
body.lang-fr .modal-footer,
body.lang-en .modal-footer,
.lang-fr .modal-footer,
.lang-en .modal-footer {
  justify-content: flex-start !important;
}

body.lang-ar .modal-footer,
.lang-ar .modal-footer {
  justify-content: flex-start !important;
}

/* Product Modal Specific Layout - Highest Specificity */
html.lang-fr .product-modal .modal-header,
html.lang-en .product-modal .modal-header,
body.lang-fr .product-modal .modal-header,
body.lang-en .product-modal .modal-header {
  justify-content: center !important;
  flex-direction: row !important;
  text-align: center !important;
}

html.lang-fr .product-modal .modal-header h2,
html.lang-en .product-modal .modal-header h2,
body.lang-fr .product-modal .modal-header h2,
body.lang-en .product-modal .modal-header h2 {
  text-align: center !important;
  flex: 1 !important;
  margin: 0 !important;
}

html.lang-ar .product-modal .modal-header h2,
body.lang-ar .product-modal .modal-header h2 {
  text-align: right !important;
  flex: 1 !important;
}

/* Product Modal Footer Buttons - Highest Specificity */
html.lang-fr .product-modal .modal-footer,
html.lang-en .product-modal .modal-footer,
body.lang-fr .product-modal .modal-footer,
body.lang-en .product-modal .modal-footer {
  justify-content: center !important;
  gap: 10px !important;
}

html.lang-ar .product-modal .modal-footer,
body.lang-ar .product-modal .modal-footer {
  justify-content: flex-start !important;
  gap: 10px !important;
  flex-direction: row-reverse !important;
}

/* Product Modal Body Content */
.lang-fr .product-modal .modal-body,
.lang-en .product-modal .modal-body {
  direction: ltr !important;
}

.lang-ar .product-modal .modal-body {
  direction: rtl !important;
}



.lang-ar .product-modal .barcode-actions {
  justify-content: flex-end !important;
  direction: rtl !important;
}

/* Barcode Help Text */
.lang-fr .product-modal .barcode-help,
.lang-en .product-modal .barcode-help {
  text-align: right !important;
  direction: ltr !important;
}

.lang-ar .product-modal .barcode-help {
  text-align: right !important;
  direction: rtl !important;
}

/* Form Grid */
.lang-fr .product-modal .form-grid,
.lang-en .product-modal .form-grid {
  direction: ltr !important;
}

.lang-ar .product-modal .form-grid {
  direction: rtl !important;
}

/* Form Groups */
.lang-fr .product-modal .form-group,
.lang-en .product-modal .form-group {
  text-align: right !important;
  direction: ltr !important;
}

.lang-ar .product-modal .form-group {
  text-align: right !important;
  direction: rtl !important;
}

/* Form Labels */
.lang-fr .product-modal .form-group label,
.lang-en .product-modal .form-group label {
  text-align: right !important;
  display: block !important;
}

.lang-ar .product-modal .form-group label {
  text-align: right !important;
  display: block !important;
}

/* Form Inputs */
.lang-fr .product-modal .form-group input,
.lang-fr .product-modal .form-group select,
.lang-en .product-modal .form-group input,
.lang-en .product-modal .form-group select {
  text-align: right !important;
  direction: ltr !important;
}

.lang-ar .product-modal .form-group input,
.lang-ar .product-modal .form-group select {
  text-align: right !important;
  direction: rtl !important;
}





/* Sales Summary Cards - Floating */
.sales-summary {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  gap: 20px;
  z-index: 1000;
  pointer-events: none;
}

.sales-summary .summary-card {
  pointer-events: auto;
  min-width: 180px;
  max-width: 220px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  transform: translateY(100%);
  animation: slideInFromBottom 0.6s ease-out forwards;
}

.sales-summary .summary-card:nth-child(1) {
  animation-delay: 0.1s;
}

.sales-summary .summary-card:nth-child(2) {
  animation-delay: 0.2s;
}

.sales-summary .summary-card:nth-child(3) {
  animation-delay: 0.3s;
}

.sales-summary .summary-card:nth-child(4) {
  animation-delay: 0.4s;
}

.sales-summary .summary-card:hover {
  transform: translateY(0) scale(1.05);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

/* Sales specific card colors */
.sales-summary .summary-card.purple {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9)) !important;
  color: white !important;
}

.sales-summary .summary-card.purple h3,
.sales-summary .summary-card.purple .summary-value {
  color: white !important;
}



/* Animation keyframes for slideInFromLeft and slideInFromRight removed - no longer needed */

/* Product Modal Styles */
.product-modal {
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}



.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.modal-footer {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid #eee;
  background: #f8f9fa;
}

/* OVERRIDE: Force modal layout for French/English - CENTERED LAYOUT */
html.lang-fr .modal-content .modal-header,
html.lang-en .modal-content .modal-header,
body.lang-fr .modal-content .modal-header,
body.lang-en .modal-content .modal-header {
  position: relative !important;
  display: flex !important;
  padding: 20px !important;
  border-bottom: 1px solid #dee2e6 !important;
  min-height: 60px !important;
  justify-content: center !important;
  flex-direction: row !important;
}

html.lang-fr .modal-content .modal-header h2,
html.lang-en .modal-content .modal-header h2,
body.lang-fr .modal-content .modal-header h2,
body.lang-en .modal-content .modal-header h2 {
  text-align: center !important;
  margin: 0 !important;
  flex: 1 !important;
}

html.lang-fr .modal-content .modal-header .modal-close,
html.lang-en .modal-content .modal-header .modal-close,
body.lang-fr .modal-content .modal-header .modal-close,
body.lang-en .modal-content .modal-header .modal-close {
  position: absolute !important;
  right: 20px !important;
  top: 20px !important;
  width: 30px !important;
  height: 30px !important;
}

html.lang-fr .modal-content .modal-footer,
html.lang-en .modal-content .modal-footer,
body.lang-fr .modal-content .modal-footer,
body.lang-en .modal-content .modal-footer {
  display: flex !important;
  gap: 10px !important;
  justify-content: center !important;
  padding: 20px !important;
  border-top: 1px solid #eee !important;
}

/* Summary Cards for Inventory */
.summary-card.red {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.summary-card.red .summary-value {
  color: white;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    order: 2;
  }

  .main-content {
    order: 1;
    margin-right: 0;
    padding: 10px;
  }

  .nav-menu {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    padding: 10px;
  }

  .nav-item {
    flex: 1;
    min-width: 120px;
    margin: 5px;
    text-align: center;
  }

  .summary-cards {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  /* Enhanced Table Responsiveness */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
  }

  .table-container::before {
    content: "← اسحب للمزيد";
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(22, 160, 133, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10;
    animation: fadeInOut 3s ease-in-out infinite;
  }

  .data-table {
    min-width: 800px;
    font-size: 14px;
  }

  .data-table th,
  .data-table td {
    padding: 8px 12px;
    white-space: nowrap;
  }

  /* Hide less important columns on mobile */
  .data-table th:nth-child(3),
  .data-table td:nth-child(3),
  .data-table th:nth-child(5),
  .data-table td:nth-child(5) {
    display: none;
  }

  /* Action buttons optimization */
  .action-buttons-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 80px;
  }

  .action-buttons-group .btn {
    padding: 4px 8px;
    font-size: 12px;
    min-width: 70px;
  }

  .modal-content {
    width: 95%;
    margin: 10px;
    max-height: 95vh;
  }

  .sales-modal {
    width: 98%;
    max-height: 95vh;
  }

  /* Inventory Table Responsive */
  .inventory-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    position: relative;
  }

  .inventory-table-container::before {
    content: "← اسحب لرؤية المزيد";
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(52, 152, 219, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10;
    animation: fadeInOut 3s ease-in-out infinite;
  }

  .inventory-table {
    min-width: 800px;
    font-size: 12px;
    width: 100%;
  }

  .inventory-table th,
  .inventory-table td {
    padding: 6px 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Hide less critical inventory columns on mobile - Keep barcode visible */
  .inventory-table th:nth-child(8), /* Min Stock */
  .inventory-table td:nth-child(8),
  .inventory-table th:nth-child(9), /* Total Value */
  .inventory-table td:nth-child(9) {
    display: none;
  }

  /* Make barcode column more compact on mobile */
  .inventory-table th:nth-child(3), /* Barcode */
  .inventory-table td:nth-child(3) {
    max-width: 80px;
    min-width: 60px;
    font-size: 10px;
    padding: 4px;
  }

  /* Make product name column flexible */
  .inventory-table th:nth-child(2),
  .inventory-table td:nth-child(2) {
    max-width: 150px;
    min-width: 120px;
  }

  /* Sellers Table Responsive */
  .sellers-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    position: relative;
  }

  .sellers-table {
    min-width: 600px;
    font-size: 14px;
  }

  .sellers-table th,
  .sellers-table td {
    padding: 10px 8px;
  }

  /* Search and Filter Controls */
  .inventory-controls {
    flex-direction: column;
    gap: 15px;
  }

  .search-section {
    flex-direction: column;
    gap: 10px;
  }

  .category-filter-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .search-input,
  .filter-select {
    width: 100%;
    min-width: auto;
  }

  .page-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .page-header h1 {
    font-size: 1.5rem;
  }

  .header-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
  }

  .header-actions .btn {
    width: 100%;
    justify-content: center;
  }

  /* Responsive floating cards */
  .inventory-summary {
    position: relative !important;
    bottom: auto !important;
    left: auto !important;
    transform: none !important;
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 10px !important;
    margin: 20px 0 !important;
    z-index: auto !important;
  }

  .inventory-summary .summary-card {
    position: relative !important;
    transform: none !important;
    animation: none !important;
    min-width: auto !important;
    max-width: none !important;
  }

  /* Responsive left navigation */
  .left-nav {
    position: relative !important;
    left: auto !important;
    top: auto !important;
    transform: none !important;
    flex-direction: row !important;
    justify-content: flex-start !important;
    margin: 20px 0 !important;
    gap: 10px !important;
  }

  .left-nav-btn {
    position: relative !important;
    transform: none !important;
    animation: none !important;
    width: 50px !important;
    height: 50px !important;
    font-size: 20px !important;
  }

  /* Responsive sales summary */
  .sales-summary {
    position: relative !important;
    bottom: auto !important;
    left: auto !important;
    transform: none !important;
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 10px !important;
    margin: 20px 0 !important;
    z-index: auto !important;
  }

  .sales-summary .summary-card {
    position: relative !important;
    transform: none !important;
    animation: none !important;
    min-width: auto !important;
    max-width: none !important;
  }
}

@media (max-width: 480px) {
  .nav-item {
    min-width: 100px;
    font-size: 0.8rem;
  }

  .nav-item .nav-icon {
    font-size: 1.2rem;
  }

  .summary-card {
    padding: 15px;
  }

  .summary-value {
    font-size: 1.2rem;
  }

  .btn {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .form-group label {
    font-size: 0.9rem;
  }

  .form-group input,
  .form-group select {
    padding: 8px;
    font-size: 0.9rem;
  }

  .barcode-input {
    font-size: 14px;
    padding: 10px;
  }

  /* Enhanced Table Responsiveness for Small Screens */
  .data-table {
    min-width: 600px;
    font-size: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 6px 8px;
  }

  /* Hide even more columns on very small screens */
  .data-table th:nth-child(4),
  .data-table td:nth-child(4),
  .data-table th:nth-child(6),
  .data-table td:nth-child(6) {
    display: none;
  }

  .inventory-table {
    min-width: 600px;
    font-size: 11px;
    width: 100%;
  }

  .inventory-table th,
  .inventory-table td {
    padding: 4px 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Hide additional inventory columns on very small screens - Keep barcode */
  .inventory-table th:nth-child(4), /* Category */
  .inventory-table td:nth-child(4),
  .inventory-table th:nth-child(5), /* Buy Price */
  .inventory-table td:nth-child(5) {
    display: none;
  }

  /* Extra compact barcode on very small screens */
  .inventory-table th:nth-child(3), /* Barcode */
  .inventory-table td:nth-child(3) {
    max-width: 60px;
    min-width: 50px;
    font-size: 9px;
    padding: 2px;
  }

  /* Compact product name for very small screens */
  .inventory-table th:nth-child(2),
  .inventory-table td:nth-child(2) {
    max-width: 100px;
    min-width: 80px;
  }

  /* Action buttons for small screens */
  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .action-buttons .btn-icon {
    padding: 6px;
    font-size: 14px;
    min-width: 35px;
    height: 35px;
  }

  .action-buttons-group .btn {
    padding: 3px 6px;
    font-size: 11px;
    min-width: 60px;
  }

  /* Dropdown menus for small screens */
  .dropdown-container {
    width: 100%;
  }

  .dropdown-menu {
    width: 100%;
    max-width: none;
  }

  /* Small screen cards */
  .inventory-summary {
    grid-template-columns: 1fr !important;
  }

  /* Small screen sales summary */
  .sales-summary {
    grid-template-columns: 1fr !important;
  }

  /* Settings cards responsive */
  .settings-cards {
    grid-template-columns: 1fr;
  }

  .setting-card {
    padding: 15px;
    flex-direction: column;
    text-align: center;
  }

  .setting-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
}

/* Print Styles */
@media print {
  .sidebar,
  .left-nav,
  .page-header button,
  .modal-footer,
  .btn {
    display: none !important;
  }

  .main-content {
    margin-right: 0;
    width: 100%;
  }

  .modal-overlay {
    position: static;
    background: none;
  }

  .modal-content {
    box-shadow: none;
    border: none;
    width: 100%;
    max-width: none;
  }
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 400px;
}

.toast {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  transform: translateX(100%);
  animation: toast-slide-in 0.3s ease-out forwards;
  cursor: pointer;
  position: relative;
  border-left: 4px solid;
  backdrop-filter: blur(10px);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.toast-success {
  border-left-color: #10b981;
}

.toast-error {
  border-left-color: #ef4444;
}

.toast-warning {
  border-left-color: #f59e0b;
}

.toast-info {
  border-left-color: #3b82f6;
}

.toast-content {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.toast-message {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  flex: 1;
  line-height: 1.4;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.toast-close {
  background: none;
  border: none;
  font-size: 18px;
  font-weight: bold;
  color: #6b7280;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.toast-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.toast-clear-all {
  margin-top: 10px;
  text-align: center;
}

.toast-clear-all .btn {
  font-size: 12px;
  padding: 6px 12px;
}

.toast-close {
  background: none;
  border: none;
  font-size: 18px;
  color: #9ca3af;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.toast-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.toast-progress {
  height: 3px;
  background: #f3f4f6;
  position: relative;
  overflow: hidden;
}

.toast-progress-bar {
  height: 100%;
  width: 100%;
  transform-origin: left;
}

.toast-success .toast-progress-bar {
  background: linear-gradient(90deg, #10b981, #059669);
}

.toast-error .toast-progress-bar {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.toast-warning .toast-progress-bar {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.toast-info .toast-progress-bar {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

@keyframes toast-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes toast-progress {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

/* Large Screen Optimization - For Electron and Desktop */
@media screen and (min-width: 1200px) {
  /* Inventory table optimization for large screens */
  .inventory-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    max-width: 100%;
  }

  .inventory-table {
    min-width: 1200px;
    font-size: 14px;
    width: 100%;
    table-layout: fixed;
  }

  .inventory-table th,
  .inventory-table td {
    padding: 12px 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Show all columns on large screens */
  .inventory-table th,
  .inventory-table td {
    display: table-cell !important;
  }

  /* Optimize column widths for large screens */
  .inventory-table th:nth-child(1), /* Product ID */
  .inventory-table td:nth-child(1) {
    width: 80px;
    min-width: 80px;
  }

  .inventory-table th:nth-child(2), /* Product Name */
  .inventory-table td:nth-child(2) {
    width: 200px;
    min-width: 200px;
  }

  .inventory-table th:nth-child(3), /* Barcode */
  .inventory-table td:nth-child(3) {
    width: 120px;
    min-width: 120px;
    font-size: 13px;
  }

  .inventory-table th:nth-child(4), /* Category */
  .inventory-table td:nth-child(4) {
    width: 120px;
    min-width: 120px;
  }

  .inventory-table th:nth-child(5), /* Buy Price */
  .inventory-table td:nth-child(5) {
    width: 100px;
    min-width: 100px;
    text-align: center;
  }

  .inventory-table th:nth-child(6), /* Sell Price */
  .inventory-table td:nth-child(6) {
    width: 100px;
    min-width: 100px;
    text-align: center;
  }

  .inventory-table th:nth-child(7), /* Quantity */
  .inventory-table td:nth-child(7) {
    width: 100px;
    min-width: 100px;
    text-align: center;
  }

  .inventory-table th:nth-child(8), /* Min Stock */
  .inventory-table td:nth-child(8) {
    width: 90px;
    min-width: 90px;
    text-align: center;
  }

  .inventory-table th:nth-child(9), /* Total Value */
  .inventory-table td:nth-child(9) {
    width: 120px;
    min-width: 120px;
    text-align: center;
  }

  .inventory-table th:nth-child(10), /* Status */
  .inventory-table td:nth-child(10) {
    width: 100px;
    min-width: 100px;
    text-align: center;
  }

  .inventory-table th:nth-child(11), /* Actions */
  .inventory-table td:nth-child(11) {
    width: 160px;
    min-width: 160px;
    text-align: center;
  }
}

/* Extra Large Screen Optimization - For very large monitors */
@media screen and (min-width: 1400px) {
  .inventory-table {
    min-width: 1400px;
    font-size: 15px;
  }

  .inventory-table th,
  .inventory-table td {
    padding: 14px 10px;
  }

  /* Wider columns for extra large screens */
  .inventory-table th:nth-child(1), /* Product ID */
  .inventory-table td:nth-child(1) {
    width: 90px;
    min-width: 90px;
  }

  .inventory-table th:nth-child(2), /* Product Name */
  .inventory-table td:nth-child(2) {
    width: 250px;
    min-width: 250px;
  }

  .inventory-table th:nth-child(3), /* Barcode */
  .inventory-table td:nth-child(3) {
    width: 140px;
    min-width: 140px;
  }

  .inventory-table th:nth-child(4), /* Category */
  .inventory-table td:nth-child(4) {
    width: 140px;
    min-width: 140px;
  }

  .inventory-table th:nth-child(5), /* Buy Price */
  .inventory-table td:nth-child(5),
  .inventory-table th:nth-child(6), /* Sell Price */
  .inventory-table td:nth-child(6) {
    width: 120px;
    min-width: 120px;
  }

  .inventory-table th:nth-child(7), /* Quantity */
  .inventory-table td:nth-child(7),
  .inventory-table th:nth-child(8), /* Min Stock */
  .inventory-table td:nth-child(8) {
    width: 110px;
    min-width: 110px;
  }

  .inventory-table th:nth-child(9), /* Total Value */
  .inventory-table td:nth-child(9) {
    width: 140px;
    min-width: 140px;
  }

  .inventory-table th:nth-child(10), /* Status */
  .inventory-table td:nth-child(10) {
    width: 120px;
    min-width: 120px;
  }

  .inventory-table th:nth-child(11), /* Actions */
  .inventory-table td:nth-child(11) {
    width: 180px;
    min-width: 180px;
  }

  /* Compact action buttons for Electron */
  .action-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .action-buttons .btn-icon {
    padding: 6px;
    font-size: 12px;
    min-width: 30px;
    height: 30px;
  }
}

/* Card-based layout for very small screens */
@media (max-width: 360px) {
  /* Convert tables to card layout on very small screens */
  .table-container {
    display: none;
  }

  .mobile-cards-container {
    display: block;
  }

  .mobile-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--primary-color);
  }

  .mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
  }

  .mobile-card-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 16px;
  }

  .mobile-card-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }

  .mobile-card-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 12px;
  }

  .mobile-card-field {
    display: flex;
    flex-direction: column;
  }

  .mobile-card-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
  }

  .mobile-card-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
  }

  .mobile-card-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .mobile-card-actions .btn {
    flex: 1;
    min-width: 80px;
    padding: 8px 12px;
    font-size: 12px;
  }

  /* Enhanced touch targets */
  .btn {
    min-height: 44px;
    min-width: 44px;
  }

  .btn-icon {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Larger form inputs for touch */
  .form-group input,
  .form-group select,
  .form-group textarea {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Enhanced Scroll to Top Button - Modern Design */
.scroll-to-top {
  position: fixed !important;
  bottom: 30px !important;
  right: 30px !important;
  width: 60px !important;
  height: 60px !important;
  background: linear-gradient(135deg, #16a085, #138d75) !important;
  color: white !important;
  border: none !important;
  border-radius: 50% !important;
  font-size: 24px !important;
  font-weight: bold !important;
  cursor: pointer !important;
  box-shadow: 0 8px 25px rgba(22, 160, 133, 0.4) !important;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
  z-index: 9999 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  opacity: 1 !important;
  visibility: visible !important;
  backdrop-filter: blur(10px) !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
}

.scroll-to-top::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.scroll-to-top:hover::before {
  opacity: 1;
}

.scroll-to-top:hover {
  transform: translateY(-5px) scale(1.15);
  box-shadow: 0 12px 35px rgba(22, 160, 133, 0.6);
  background: linear-gradient(135deg, #138d75, #117a65) !important;
}

.scroll-to-top:active {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(22, 160, 133, 0.4);
}

/* Arabic - Scroll button on the left */
.lang-ar .scroll-to-top {
  left: 30px;
  right: auto;
}

/* French and English - Scroll button on the right */
.lang-fr .scroll-to-top,
.lang-en .scroll-to-top {
  right: 30px;
  left: auto;
}

/* Arabic RTL - Position scroll button on the LEFT */
.lang-ar .scroll-to-top {
  left: 30px !important;
  right: auto !important;
}

/* French/English LTR - Position scroll button on the RIGHT */
.lang-fr .scroll-to-top,
.lang-en .scroll-to-top {
  right: 30px !important;
  left: auto !important;
}

/* Enhanced Responsive scroll button */
@media (max-width: 768px) {
  .scroll-to-top {
    bottom: 20px !important;
    width: 50px !important;
    height: 50px !important;
    font-size: 20px !important;
  }

  .lang-ar .scroll-to-top {
    left: 20px !important;
    right: auto !important;
  }

  .lang-fr .scroll-to-top,
  .lang-en .scroll-to-top {
    right: 20px !important;
    left: auto !important;
  }
}

@media (max-width: 480px) {
  .scroll-to-top {
    bottom: 15px !important;
    width: 45px !important;
    height: 45px !important;
    font-size: 18px !important;
  }

  .lang-ar .scroll-to-top {
    left: 15px !important;
    right: auto !important;
  }

  .lang-fr .scroll-to-top,
  .lang-en .scroll-to-top {
    right: 15px !important;
    left: auto !important;
  }
}

/* Settings Page */
.settings-page {
  padding: 20px;
}

.settings-tabs {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.settings-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.section-header h2 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin: 0;
}

.settings-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.setting-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 10px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  border-left: 4px solid #007bff;
  transition: all 0.3s ease;
}

.setting-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.15);
}

.setting-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.setting-info h3 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.setting-info p {
  margin: 0;
  color: #6c757d;
  font-weight: 500;
}

/* Sellers Table */
.sellers-table-container {
  overflow-x: auto;
  margin-top: 20px;
}

.sellers-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sellers-table th,
.sellers-table td {
  padding: 12px 15px;
  text-align: right;
  border-bottom: 1px solid #e9ecef;
}

.sellers-table th {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.sellers-table tr:hover {
  background: #f8f9fa;
}

.role-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.role-badge.admin {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

.role-badge.seller {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-badge.active {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
}

.status-badge.inactive {
  background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
  color: white;
}

.discount-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  border: 1px solid #ffeaa7;
  display: inline-block;
}

/* Settings Page Language Support */
.settings-page.lang-fr,
.settings-page.lang-en {
  direction: ltr;
}

.settings-page.lang-fr .page-header,
.settings-page.lang-en .page-header {
  text-align: right;
  direction: ltr;
}

.settings-page.lang-fr .section-header,
.settings-page.lang-en .section-header {
  text-align: left;
  flex-direction: row;
  direction: ltr;
}

.settings-page.lang-fr .section-header h2,
.settings-page.lang-en .section-header h2 {
  text-align: left;
}

/* Settings Cards Language Support */
.settings-page.lang-fr .settings-cards,
.settings-page.lang-en .settings-cards {
  direction: ltr;
}

.settings-page.lang-fr .setting-card,
.settings-page.lang-en .setting-card {
  direction: ltr;
  text-align: left;
}

.settings-page.lang-fr .setting-info,
.settings-page.lang-en .setting-info {
  text-align: left;
}

.settings-page.lang-fr .setting-info h3,
.settings-page.lang-en .setting-info h3 {
  text-align: left;
}

.settings-page.lang-fr .setting-info p,
.settings-page.lang-en .setting-info p {
  text-align: left;
}

/* Settings Table Language Support */
.sellers-table.table-ltr {
  direction: ltr;
}

.sellers-table.table-ltr th,
.sellers-table.table-ltr td {
  text-align: left;
}

.sellers-table.table-ltr .action-buttons {
  justify-content: flex-start;
}

.settings-page.lang-fr .sellers-table-container,
.settings-page.lang-en .sellers-table-container {
  direction: ltr;
}

/* Settings Modal Language Support */
.settings-modal.lang-fr,
.settings-modal.lang-en {
  direction: ltr;
}

.settings-modal.lang-fr .modal-header,
.settings-modal.lang-en .modal-header {
  text-align: left;
}

.settings-modal.lang-fr .form-grid,
.settings-modal.lang-en .form-grid {
  direction: ltr;
}

.settings-modal.lang-fr .form-group label,
.settings-modal.lang-en .form-group label {
  text-align: left;
}

/* Modal Footer Language Support */
.modal-footer-ltr {
  flex-direction: row-reverse;
  text-align: left;
}

.modal-header-ltr {
  text-align: left;
}

.modal-header-ltr h2 {
  text-align: left;
}

.section-header-ltr {
  text-align: left;
  flex-direction: row;
}

.page-header-ltr {
  text-align: right;
}

.page-header-ltr h1 {
  text-align: right;
}

/* Sales Invoice Modal Language Support */
.sales-modal-landscape.lang-fr,
.sales-modal-landscape.lang-en {
  direction: ltr;
}

.sales-modal-landscape.lang-fr .modal-title-section,
.sales-modal-landscape.lang-en .modal-title-section {
  text-align: left;
}

.sales-modal-landscape.lang-fr .shortcuts-display,
.sales-modal-landscape.lang-en .shortcuts-display {
  justify-content: flex-start;
}

.sales-modal-landscape.lang-fr .invoice-header-section,
.sales-modal-landscape.lang-en .invoice-header-section {
  direction: ltr;
}

.sales-modal-landscape.lang-fr .form-row,
.sales-modal-landscape.lang-en .form-row {
  direction: ltr;
}

.sales-modal-landscape.lang-fr .totals-summary,
.sales-modal-landscape.lang-en .totals-summary {
  text-align: left;
}

.action-buttons-ltr {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

/* Settings Modal */
.settings-modal {
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

/* Form Help Text */
.form-help-text {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 0.25rem;
  font-style: italic;
  line-height: 1.3;
}

.lang-ar .form-help-text {
  text-align: right;
  font-family: 'Cairo', sans-serif;
}

.lang-fr .form-help-text,
.lang-en .form-help-text {
  text-align: left;
}

.logo-preview {
  margin-top: 10px;
  text-align: center;
}

.logo-preview img {
  max-width: 150px;
  max-height: 100px;
  border-radius: 8px;
  border: 2px solid #e9ecef;
}

/* Seller Modal */
.seller-modal {
  max-width: 500px;
}

/* Invoice Modal */
.invoice-modal {
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.invoice-view {
  background: white;
  padding: 30px;
  border-radius: 8px;
}

.invoice-header-view {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
}

.store-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.store-logo {
  width: 80px;
  height: 80px;
  object-fit: contain;
  border-radius: 8px;
}

.store-details h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.store-details p {
  margin: 5px 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.invoice-info {
  text-align: left;
}

.customer-info {
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.customer-info h3 {
  margin: 0 0 15px 0;
  color: var(--primary-color);
  font-size: 1.1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-item strong {
  color: var(--text-primary);
  min-width: 120px;
}

.invoice-items {
  margin: 20px 0;
}

.invoice-items h3 {
  margin: 0 0 15px 0;
  color: var(--primary-color);
  font-size: 1.1rem;
}

.invoice-totals {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.totals-section {
  max-width: 400px;
  margin-left: auto;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.total-row:last-child {
  border-bottom: none;
}

.total-row.final-total {
  font-weight: bold;
  font-size: 1.1rem;
  color: var(--primary-color);
  border-top: 2px solid var(--primary-color);
  margin-top: 10px;
  padding-top: 15px;
}

.payment-method.cash {
  color: var(--success-color);
  font-weight: 600;
}

.payment-method.credit {
  color: var(--warning-color);
  font-weight: 600;
}

/* Invoice Modal Specific Styles */
.invoice-modal {
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
}

.invoice-view {
  padding: 0;
}

.invoice-header-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.store-details h1 {
  color: var(--primary-color);
  margin: 0 0 10px 0;
  font-size: 1.5rem;
}

.store-details p {
  margin: 5px 0;
  color: var(--text-secondary);
}

.invoice-info h2 {
  color: var(--primary-color);
  margin: 0 0 15px 0;
  font-size: 1.3rem;
}

.invoice-info p {
  margin: 8px 0;
  color: var(--text-primary);
}

.invoice-info strong {
  color: var(--primary-color);
}

/* Responsive design for invoice modal */
@media (max-width: 768px) {
  .invoice-modal {
    max-width: 95vw;
    margin: 10px;
  }

  .invoice-header-view {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}

.invoice-info h2 {
  margin: 0 0 15px 0;
  color: #007bff;
  font-size: 1.8rem;
}

.invoice-info p {
  margin: 5px 0;
  color: #2c3e50;
  font-size: 0.9rem;
}

.invoice-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

.invoice-table th,
.invoice-table td {
  padding: 12px;
  text-align: right;
  border-bottom: 1px solid #e9ecef;
}

.invoice-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.invoice-totals {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.totals-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 1rem;
}

.totals-row.total {
  font-size: 1.2rem;
  font-weight: bold;
  color: #007bff;
  border-top: 2px solid #007bff;
  padding-top: 10px;
  margin-top: 15px;
}

.invoice-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
  color: #6c757d;
  font-size: 0.9rem;
}

/* Print Styles */
@media print {
  .modal-overlay,
  .modal-header,
  .modal-footer,
  .toast-container {
    display: none !important;
  }

  .invoice-view {
    box-shadow: none;
    padding: 0;
  }

  .invoice-modal {
    max-width: none;
    max-height: none;
    overflow: visible;
  }
}

/* Toast Responsive */
@media (max-width: 768px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }

  .toast {
    margin: 0;
  }

  .toast-message {
    font-size: 13px;
  }

  .settings-cards {
    grid-template-columns: 1fr;
  }

  .invoice-header-view {
    flex-direction: column;
    gap: 20px;
  }

  .sellers-table-container {
    font-size: 0.8rem;
  }
}

/* Responsive Design for Voir les Transactions Modal */
@media (max-width: 1200px) {
  .nouveau-bon-modal {
    width: 95%;
    max-width: 1000px;
    height: 90vh;
    margin: 5vh auto;
  }
}

@media (max-width: 768px) {
  .nouveau-bon-modal {
    width: 98%;
    max-width: none;
    height: 95vh;
    margin: 2.5vh auto;
  }

  /* Make content sections stack vertically on mobile */
  .nouveau-bon-modal .content-container {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  /* Adjust table container for mobile */
  .table-container-scrollable {
    max-height: 300px !important;
    font-size: 0.85rem;
  }

  /* Responsive summary cards */
  .nouveau-bon-modal .summary-cards {
    grid-template-columns: 1fr !important;
    gap: 0.5rem !important;
  }
}

/* Additional Responsive Design for Repair Management Pages */
@media (max-width: 1200px) {
  /* Make repair order tables more responsive */
  .data-table {
    font-size: 0.9rem;
  }

  .data-table th,
  .data-table td {
    padding: 0.75rem 0.5rem;
  }

  /* Responsive action buttons */
  .action-buttons-group {
    gap: 0.25rem;
  }

  .action-buttons-group .btn {
    min-width: 32px;
    height: 32px;
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  /* Stack content sections vertically on mobile */
  .content-container {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  /* Make tables more mobile-friendly */
  .table-container-scrollable {
    max-height: 250px !important;
    font-size: 0.8rem;
  }

  .data-table th,
  .data-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.75rem;
  }

  /* Responsive search and filter sections */
  .search-section {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .search-input {
    width: 100% !important;
    min-width: auto !important;
  }

  /* Responsive action buttons for mobile */
  .action-buttons-group {
    gap: 2px;
    min-width: 100px;
  }

  .action-buttons-group .btn {
    min-width: 24px !important;
    height: 24px !important;
    font-size: 0.7rem !important;
    padding: 2px 4px !important;
  }
}

/* Customer Info Display */
.customer-info-display {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  border-left: 4px solid #007bff;
}

.customer-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.customer-detail {
  font-size: 0.9rem;
}

.customer-detail strong {
  color: #2c3e50;
  margin-right: 5px;
}

/* Enhanced Customer Table Action Buttons - Match Sales Management Style */
.customers-page .action-buttons-group {
  display: flex;
  gap: 3px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  min-width: 140px;
}

.customers-page .action-buttons-group .btn {
  padding: 6px 8px !important;
  font-size: 14px !important;
  min-width: 30px !important;
  height: 30px !important;
  border-radius: 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 1px solid transparent !important;
  transition: all 0.2s ease;
  margin: 0 !important;
}

/* Customer Table Action Button Colors - Green Font Colors */
.customers-page .data-table .action-buttons-group .btn.btn-primary,
.customers-page .action-buttons-group .btn.btn-primary {
  background: transparent !important;
  color: #00b9ae !important;
  border: 1px solid #00b9ae !important;
  font-size: 16px !important;
}

.customers-page .data-table .action-buttons-group .btn.btn-primary:hover,
.customers-page .action-buttons-group .btn.btn-primary:hover {
  background: #00b9ae !important;
  color: white !important;
  border-color: #00b9ae !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 185, 174, 0.3);
}

.customers-page .data-table .action-buttons-group .btn.btn-danger,
.customers-page .action-buttons-group .btn.btn-danger {
  background: transparent !important;
  color: #dc3545 !important;
  border: 1px solid #dc3545 !important;
  font-size: 16px !important;
}

.customers-page .data-table .action-buttons-group .btn.btn-danger:hover,
.customers-page .action-buttons-group .btn.btn-danger:hover {
  background: #dc3545 !important;
  color: white !important;
  border-color: #dc3545 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Repair Orders Table Action Button Colors - Match Header */
.repair-orders-table-container .action-buttons-group .btn.btn-primary {
  background: #498C8A !important;
  color: white !important;
  border: 1px solid #498C8A !important;
}

.repair-orders-table-container .action-buttons-group .btn.btn-primary:hover {
  background: #3a7270 !important;
  border-color: #3a7270 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(73, 140, 138, 0.3);
}

.repair-orders-table-container .action-buttons-group .btn.btn-danger {
  background: #dc3545 !important;
  color: white !important;
  border: 1px solid #dc3545 !important;
}

.repair-orders-table-container .action-buttons-group .btn.btn-danger:hover {
  background: #c82333 !important;
  border-color: #c82333 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Customer Table Enhanced Search - Header Color Matching */
.customers-page .inventory-controls {
  margin: 20px 0;
  background: var(--primary-color);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.customers-page .search-section {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.customers-page .search-input {
  flex: 1;
  min-width: 300px;
  padding: 12px 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  transition: all 0.3s ease;
}

.customers-page .search-input:focus {
  border-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
  outline: none;
  background: white;
}

.customers-page .search-input::placeholder {
  color: #666;
  opacity: 0.8;
}

/* RTL/LTR Support for Customer Search */
.lang-ar .customers-page .search-section {
  direction: rtl;
  text-align: right;
}

.lang-fr .customers-page .search-section,
.lang-en .customers-page .search-section {
  direction: ltr;
  text-align: left;
}

.lang-ar .customers-page .search-input {
  text-align: right;
  padding-right: 16px;
  padding-left: 12px;
}

.lang-fr .customers-page .search-input,
.lang-en .customers-page .search-input {
  text-align: left;
  padding-left: 16px;
  padding-right: 12px;
}

/* Clear filter button styling */
.customers-page .btn-secondary.btn-xs {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
}

.customers-page .btn-secondary.btn-xs:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* Responsive Customer Table Actions */
@media (max-width: 768px) {
  .customers-page .action-buttons-group {
    gap: 2px;
    min-width: 120px;
  }

  .customers-page .action-buttons-group .btn {
    min-width: 28px !important;
    height: 28px !important;
    font-size: 12px !important;
    padding: 4px 6px !important;
  }

  .customers-page .search-input {
    min-width: 250px;
    font-size: 13px;
    padding: 10px 14px;
  }
}

@media (max-width: 480px) {
  .customers-page .action-buttons-group {
    flex-direction: column;
    gap: 2px;
    min-width: 35px;
  }

  .customers-page .search-input {
    min-width: 200px;
    font-size: 12px;
    padding: 8px 12px;
  }

  .customers-page .inventory-controls {
    padding: 10px;
  }
}

/* Customer Table RTL/LTR Support */
.customers-page .data-table.table-ltr {
  direction: ltr;
}

.customers-page .data-table.table-ltr th,
.customers-page .data-table.table-ltr td {
  text-align: left;
}

.lang-ar .customers-page .data-table {
  direction: rtl;
}

.lang-ar .customers-page .data-table th,
.lang-ar .customers-page .data-table td {
  text-align: right;
}

/* Action column always centered regardless of language */
.customers-page .data-table th:last-child,
.customers-page .data-table td:last-child {
  text-align: center !important;
}

/* Checkbox column always centered */
.customers-page .data-table .checkbox-column {
  text-align: center !important;
  width: 50px;
  min-width: 50px;
}

/* Enhanced table header styling for customers */
.customers-page .data-table th {
  background: var(--primary-color);
  color: white;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.customers-page .data-table tr:hover {
  background: rgba(111, 66, 193, 0.05);
}

/* Sales Page Enhanced Search - Header Color Matching */
.sales-page .inventory-controls {
  margin: 20px 0;
  background: linear-gradient(135deg, #28a745, #20c997);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sales-page .search-section {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.sales-page .search-input {
  flex: 1;
  min-width: 300px;
  padding: 12px 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  transition: all 0.3s ease;
}

.sales-page .search-input:focus {
  border-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
  outline: none;
  background: white;
}

.sales-page .search-input::placeholder {
  color: #666;
  opacity: 0.8;
}

/* RTL/LTR Support for Sales Search */
.lang-ar .sales-page .search-section {
  direction: rtl;
  text-align: right;
}

.lang-fr .sales-page .search-section,
.lang-en .sales-page .search-section {
  direction: ltr;
  text-align: left;
}

.lang-ar .sales-page .search-input {
  text-align: right;
  padding-right: 16px;
  padding-left: 12px;
}

.lang-fr .sales-page .search-input,
.lang-en .sales-page .search-input {
  text-align: left;
  padding-left: 16px;
  padding-right: 12px;
}

/* Clear filter button styling for sales */
.sales-page .btn-secondary.btn-xs {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
}

.sales-page .btn-secondary.btn-xs:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments for sales search */
@media (max-width: 768px) {
  .sales-page .search-section {
    flex-direction: column;
    gap: 15px;
  }

  .sales-page .search-input {
    min-width: 250px;
    font-size: 13px;
    padding: 10px 14px;
  }
}

@media (max-width: 480px) {
  .sales-page .search-input {
    min-width: 200px;
    font-size: 12px;
    padding: 8px 12px;
  }

  .sales-page .inventory-controls {
    padding: 10px;
  }
}

/* Sales Table RTL/LTR Support */
.sales-page .data-table.table-ltr {
  direction: ltr;
}

.sales-page .data-table.table-ltr th,
.sales-page .data-table.table-ltr td {
  text-align: left;
}

.lang-ar .sales-page .data-table {
  direction: rtl;
}

.lang-ar .sales-page .data-table th,
.lang-ar .sales-page .data-table td {
  text-align: right;
}

/* Action column always centered regardless of language */
.sales-page .data-table th:last-child,
.sales-page .data-table td:last-child {
  text-align: center !important;
}

/* Checkbox column always centered */
.sales-page .data-table .checkbox-column {
  text-align: center !important;
  width: 50px;
  min-width: 50px;
}

.sales-page .data-table tr:hover {
  background: rgba(40, 167, 69, 0.05);
}

/* Purchases Page Enhanced Search - Header Color Matching */
.purchases-page .inventory-controls {
  margin: 20px 0;
  background: linear-gradient(135deg, #007bff, #6610f2);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.purchases-page .search-section {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.purchases-page .search-input {
  flex: 1;
  min-width: 300px;
  padding: 12px 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  transition: all 0.3s ease;
}

.purchases-page .search-input:focus {
  border-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
  outline: none;
  background: white;
}

.purchases-page .search-input::placeholder {
  color: #666;
  opacity: 0.8;
}

/* RTL/LTR Support for Purchases Search */
.lang-ar .purchases-page .search-section {
  direction: rtl;
  text-align: right;
}

.lang-fr .purchases-page .search-section,
.lang-en .purchases-page .search-section {
  direction: ltr;
  text-align: left;
}

.lang-ar .purchases-page .search-input {
  text-align: right;
  padding-right: 16px;
  padding-left: 12px;
}

.lang-fr .purchases-page .search-input,
.lang-en .purchases-page .search-input {
  text-align: left;
  padding-left: 16px;
  padding-right: 12px;
}

/* Clear filter button styling for purchases */
.purchases-page .btn-secondary.btn-xs {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
}

.purchases-page .btn-secondary.btn-xs:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments for purchases search */
@media (max-width: 768px) {
  .purchases-page .search-section {
    flex-direction: column;
    gap: 15px;
  }

  .purchases-page .search-input {
    min-width: 250px;
    font-size: 13px;
    padding: 10px 14px;
  }
}

@media (max-width: 480px) {
  .purchases-page .search-input {
    min-width: 200px;
    font-size: 12px;
    padding: 8px 12px;
  }

  .purchases-page .inventory-controls {
    padding: 10px;
  }
}

/* Purchases Table RTL/LTR Support */
.purchases-page .data-table.table-ltr {
  direction: ltr;
}

.purchases-page .data-table.table-ltr th,
.purchases-page .data-table.table-ltr td {
  text-align: left;
}

.lang-ar .purchases-page .data-table {
  direction: rtl;
}

.lang-ar .purchases-page .data-table th,
.lang-ar .purchases-page .data-table td {
  text-align: right;
}

/* Action column always centered regardless of language */
.purchases-page .data-table th:last-child,
.purchases-page .data-table td:last-child {
  text-align: center !important;
}

/* Checkbox column always centered */
.purchases-page .data-table .checkbox-column {
  text-align: center !important;
  width: 50px;
  min-width: 50px;
}

.purchases-page .data-table tr:hover {
  background: rgba(0, 123, 255, 0.05);
}

/* Simple Customer Filter */
.simple-filter-section {
  margin: 15px 0;
  display: flex;
  align-items: center;
  gap: 10px;
  max-width: 400px;
}

.simple-filter-input {
  flex: 1;
  padding: 10px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s ease;
}

.simple-filter-input:focus {
  border-color: #007bff;
}

.clear-filter-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.clear-filter-btn:hover {
  background: #c82333;
}

/* Payment Method Styles */
.payment-method {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.payment-method.cash {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
}

.payment-method.credit {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: #212529;
}

/* Status Styles */
.status.pending {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  font-weight: 600;
}

.status.paid {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
  font-weight: 600;
}

/* Text Colors */
.text-success {
  color: #28a745 !important;
  font-weight: 600;
}

.text-danger {
  color: #dc3545 !important;
  font-weight: 600;
}

/* Button Styles - Compact Large Buttons */
.btn-large {
  padding: 6px 12px !important;
  font-size: 0.85rem !important;
  font-weight: 600;
  border-radius: 5px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-width: 100px !important;
}

.btn-large:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Sales Modal Landscape Layout */
.sales-modal-landscape,
.purchase-modal-landscape {
  max-width: 95vw;
  max-height: 95vh;
  width: 95vw;
  height: 95vh;
  margin: 2.5vh auto;
}

/* Compact Purchase Modal Layout - Smaller and Simpler */
.purchase-modal-compact {
  max-width: 70vw;
  max-height: 80vh;
  width: 70vw;
  height: auto;
  margin: 10vh auto;
  min-height: 500px;
}

.modal-body-landscape,
.purchase-modal-body {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: calc(95vh - 120px);
  overflow: hidden;
  padding: 20px;
}

.purchase-modal-body {
  overflow-y: auto;
}

.left-panel {
  flex: 0 0 400px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  padding-right: 10px;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: hidden;
}

/* Compact Form Styles */
.invoice-header-compact {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #2196f3;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
}

.customer-selection {
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #4caf50;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.product-selection-compact {
  background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #ff9800;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.1);
}

.form-grid-compact {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-grid-compact .form-group {
  margin-bottom: 0;
}

.form-grid-compact label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 5px;
}

.form-grid-compact input,
.form-grid-compact select {
  padding: 10px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-grid-compact input:focus,
.form-grid-compact select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.15);
  transform: translateY(-1px);
}

.form-grid-compact input:hover,
.form-grid-compact select:hover {
  border-color: #80bdff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Customer Info Compact */
.customer-info-compact {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 8px;
  padding: 12px;
  border-left: 4px solid #2196f3;
}

.customer-details-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 0.9rem;
}

.customer-details-compact span {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Barcode Section Compact */
.barcode-section-compact {
  background: linear-gradient(135deg, #fff3e0 0%, #f1f8e9 100%);
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #ff9800;
}

.barcode-input-large {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #ff9800;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
  background: white;
  transition: all 0.3s ease;
}

.barcode-input-large:focus {
  border-color: #f57c00;
  box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.2);
  outline: none;
}

/* Product Selection Compact */
.product-selection-compact .form-grid-compact {
  grid-template-columns: 2fr 1fr 1fr 1fr;
}

/* Product Selection Container with Search */
.product-selection-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* CLEANED UP - Removed unused barcode scanner styles */

.scanner-container h3 {
  margin: 0 0 1rem 0;
  color: #28a745;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
  font-size: 1.1rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.barcode-scanner-section .barcode-input-container {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}

.barcode-scanner-section .barcode-icon {
  position: absolute;
  left: 15px;
  font-size: 20px;
  color: #28a745;
  pointer-events: none;
  z-index: 2;
}

.barcode-scanner-section .barcode-input {
  flex: 1;
  padding: 14px 16px 14px 50px;
  background: white;
  border: 2px solid #28a745;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.barcode-scanner-section .barcode-input:focus {
  outline: none;
  border-color: #20c997;
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25), inset 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: scale(1.01);
}

.barcode-scanner-section .barcode-input::placeholder {
  color: #6c757d;
  font-style: italic;
}

/* Language-specific styles for sales barcode scanner */
.lang-ar .barcode-scanner-section .barcode-icon {
  left: auto;
  right: 15px;
}

.lang-ar .barcode-scanner-section .barcode-input {
  padding: 14px 50px 14px 16px;
  text-align: right;
}

.lang-fr .barcode-scanner-section .barcode-input,
.lang-en .barcode-scanner-section .barcode-input {
  text-align: left;
}

/* Sales Invoice Barcode Scanner - Full Width Like Photo - FIXED */
.sales-barcode-scanner-row {
  margin: 1rem 0;
  background: linear-gradient(135deg, #2c3e50, #34495e); /* Grey LCD color scheme */
  border-radius: 12px;
  border: 3px solid #1a252f;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  width: 100% !important; /* Full width like photo - FORCE */
  max-width: none !important; /* Remove max-width to allow full width - FORCE */
  min-height: 180px; /* Compact height like photo */
  display: grid !important;
  grid-template-columns: 1fr 1fr !important; /* Side by side - equal width - FORCE */
  grid-template-areas: "scanner lcd" !important; /* Default layout - FORCE */
  gap: 0;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Edit Invoice Modal - Full Width Like Photo - FIXED */
.large-modal .sales-barcode-scanner-row {
  margin: 1rem 0;
  background: linear-gradient(135deg, #2c3e50, #34495e);
  border-radius: 12px;
  border: 3px solid #1a252f;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  width: 100% !important; /* Full width like photo - FORCE */
  max-width: none !important; /* Remove max-width to allow full width - FORCE */
  min-height: 180px; /* Compact height like photo */
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  grid-template-areas: "scanner lcd" !important;
  gap: 0;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Scanner Input Section - Left Side - Compact Like Photo - FIXED */
.scanner-input-row {
  padding: 1.2rem !important; /* Smaller padding like photo - FORCE */
  border-right: 3px solid #1a252f;
  background: linear-gradient(135deg, #34495e, #2c3e50) !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
  grid-area: scanner !important;
  visibility: visible !important;
  opacity: 1 !important;
  min-height: 180px !important;
}

/* LCD Display Section - Right Side - BIGGER LCD NO TITLE - FIXED */
.lcd-display-row {
  padding: 0.5rem !important; /* Smaller padding to make LCD bigger */
  background: linear-gradient(135deg, #2c3e50, #1a252f) !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  grid-area: lcd !important;
  visibility: visible !important;
  opacity: 1 !important;
  min-height: 180px !important;
}

/* Language-specific layout for sales scanner - FIXED */
/* Default layout - Scanner LEFT, LCD RIGHT */
.sales-barcode-scanner-row .scanner-input-row {
  grid-area: scanner !important;
  display: flex !important;
  visibility: visible !important;
}

.sales-barcode-scanner-row .lcd-display-row {
  grid-area: lcd !important;
  display: flex !important;
  visibility: visible !important;
}

/* French and English - Scanner LEFT, LCD RIGHT - FIXED */
.lang-fr .sales-barcode-scanner-row,
.lang-en .sales-barcode-scanner-row {
  grid-template-areas: "scanner lcd" !important;
  display: grid !important;
}

/* Arabic - Scanner RIGHT, LCD LEFT - FIXED */
.lang-ar .sales-barcode-scanner-row {
  grid-template-areas: "lcd scanner" !important;
  display: grid !important;
}

.lang-ar .scanner-input-row {
  border-right: none !important;
  border-left: 2px solid #1a252f !important;
}

/* Row Design - No language-specific layout needed for full-width rows */

/* Scanner Row Title Styling - Compact Like Photo - FIXED */
.scanner-input-row h3 {
  margin: 0 0 12px 0 !important; /* Smaller margin like photo - FORCE */
  color: #ffffff !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important; /* Smaller gap like photo - FORCE */
  justify-content: center !important;
  font-size: 1rem !important; /* Smaller font like photo - FORCE */
  font-weight: bold !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important; /* Smaller letter spacing like photo - FORCE */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* LCD Row Title Styling - DELETED - NO MORE TITLES */
.lcd-display-row h3 {
  display: none !important; /* Hide any remaining titles */
}

/* Edit Invoice Modal - Enhanced Titles Like Photo */
.large-modal .scanner-input-row h3 {
  margin: 0 0 20px 0;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
  font-size: 1.3rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5);
}

.large-modal .lcd-display-row h3 {
  margin: 0 0 20px 0;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
  font-size: 1.3rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5);
}

.large-modal .scanner-status-active {
  color: #00ff41;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(0, 255, 65, 0.8);
}

/* Sales Invoice Modal - Enhanced Status Active Like Photo */
.scanner-status-active {
  color: #00ff41;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(0, 255, 65, 0.8);
}

/* Sales Barcode Input Styling - Compact Like Photo - FIXED */
.scanner-input-row .barcode-input-container {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important; /* Smaller gap like photo - FORCE */
  margin: 12px 0 !important; /* Smaller margin like photo - FORCE */
  visibility: visible !important;
  opacity: 1 !important;
  width: 100% !important;
}

/* Edit Invoice Modal - Enhanced Scanner Input Like Photo */
.large-modal .scanner-input-row .barcode-input-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px; /* Smaller gap like photo */
  margin: 12px 0; /* Smaller margin like photo */
}

.large-modal .scanner-input-row .barcode-input {
  flex: 1;
  padding: 12px 15px 12px 45px; /* Smaller padding like photo */
  background: linear-gradient(135deg, #0f1419, #1a252f);
  border: 3px solid #00ff41;
  border-radius: 8px; /* Smaller radius like photo */
  font-size: 16px; /* Smaller font like photo */
  font-weight: 600;
  color: #00ff41;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px; /* Smaller letter spacing like photo */
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.6);
  min-height: 45px; /* Smaller height like photo */
}

.large-modal .scanner-input-row .barcode-input:focus {
  outline: none;
  border-color: #20c997;
  box-shadow: 0 0 0 3px rgba(0, 255, 65, 0.25), inset 0 4px 12px rgba(0, 0, 0, 0.6);
  transform: scale(1.01);
}

.large-modal .scanner-input-row .barcode-input::placeholder {
  color: #7fb3d3;
  font-style: italic;
}

.large-modal .scanner-input-row .barcode-icon {
  position: absolute;
  left: 15px; /* Smaller positioning like photo */
  font-size: 18px; /* Smaller icon like photo */
  color: #00ff41;
  pointer-events: none;
  z-index: 2;
  text-shadow: 0 0 10px rgba(0, 255, 65, 0.7);
}

.scanner-input-row .barcode-icon {
  position: absolute !important;
  left: 15px !important; /* Smaller positioning like photo - FORCE */
  font-size: 18px !important; /* Smaller icon like photo - FORCE */
  color: #00ff41 !important;
  pointer-events: none !important;
  z-index: 2 !important;
  text-shadow: 0 0 10px rgba(0, 255, 65, 0.7) !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.scanner-input-row .barcode-input {
  flex: 1 !important;
  padding: 12px 15px 12px 45px !important; /* Smaller padding like photo - FORCE */
  background: linear-gradient(135deg, #0f1419, #1a252f) !important;
  border: 3px solid #00ff41 !important;
  border-radius: 8px !important; /* Smaller border radius like photo - FORCE */
  font-size: 16px !important; /* Smaller font like photo - FORCE */
  font-weight: 600 !important;
  color: #00ff41 !important;
  font-family: 'Courier New', monospace !important;
  letter-spacing: 1px !important; /* Smaller letter spacing like photo - FORCE */
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.6) !important;
  min-height: 45px !important; /* Smaller height like photo - FORCE */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.scanner-input-row .barcode-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.3), inset 0 2px 6px rgba(0, 0, 0, 0.6); /* Smaller focus shadow */
  color: #ffffff;
  background: linear-gradient(135deg, #1a252f, #2c3e50);
  transform: scale(1.01); /* Smaller scale */
}

.scanner-input-row .barcode-input::placeholder {
  color: #7fb3d3;
  font-style: italic;
  font-size: 12px; /* Smaller placeholder font */
}

/* Language-specific barcode input positioning - Compact Like Photo */
.lang-ar .scanner-input-row .barcode-icon {
  left: auto;
  right: 15px; /* Smaller positioning for Arabic like photo */
}

.lang-ar .scanner-input-row .barcode-input {
  padding: 12px 45px 12px 15px; /* Smaller padding for Arabic like photo */
  text-align: right;
}

/* Edit Invoice Modal - Language-specific layout like photo */
.large-modal.lang-ar .sales-barcode-scanner-row {
  grid-template-areas: "lcd scanner";
}

.large-modal.lang-ar .scanner-input-row {
  border-right: none;
  border-left: 3px solid #1a252f;
}

.large-modal.lang-ar .scanner-input-row .barcode-icon {
  left: auto;
  right: 15px; /* Smaller positioning like photo */
}

.large-modal.lang-ar .scanner-input-row .barcode-input {
  padding: 12px 45px 12px 15px; /* Smaller padding like photo */
  text-align: right;
}

/* Sales Barcode Actions Styling - Compact Like Photo */
.scanner-input-row .barcode-input-container .btn {
  padding: 12px 16px; /* Smaller padding like photo */
  font-size: 14px; /* Smaller font like photo */
  border-radius: 6px; /* Smaller border radius like photo */
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  min-height: 45px; /* Smaller height like photo */
  white-space: nowrap;
}

.scanner-input-row .barcode-input-container .btn:hover {
  transform: translateY(-1px); /* Smaller transform */
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4); /* Smaller hover shadow */
}

/* Edit Invoice Modal - Enhanced Clear Button Like Photo */
.large-modal .scanner-input-row .barcode-input-container .btn {
  padding: 12px 16px; /* Smaller padding like photo */
  font-size: 14px; /* Smaller font like photo */
  border-radius: 6px; /* Smaller radius like photo */
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  min-height: 45px; /* Smaller height like photo */
  white-space: nowrap;
}

.large-modal .scanner-input-row .barcode-input-container .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
}

.large-modal .scanner-input-row .barcode-input-container .btn-warning {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  border: 2px solid #d68910;
}

.large-modal .scanner-input-row .barcode-input-container .btn-warning:hover {
  background: linear-gradient(135deg, #e67e22, #d68910);
  border-color: #bf6f0a;
}

/* NEW BIGGER LCD Screen - NO TITLE */
.lcd-display-row .lcd-screen-big {
  background: linear-gradient(135deg, #0f1419, #1a252f) !important;
  border: 3px solid #00ff41 !important;
  border-radius: 8px !important;
  padding: 1.5rem !important; /* Bigger padding for bigger LCD */
  min-height: 160px !important; /* Much bigger height - almost full container */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.6) !important;
  position: relative;
  overflow: hidden;
  width: 100% !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* OLD LCD Screen - Keep for compatibility */
.lcd-display-row .lcd-screen {
  background: linear-gradient(135deg, #0f1419, #1a252f) !important;
  border: 3px solid #00ff41 !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  min-height: 100px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.6) !important;
  position: relative;
  overflow: hidden;
  width: 100% !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* NEW PRODUCT INFO LAYOUT - Name Left, Price Right */
.product-info-layout {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
  gap: 20px !important;
  color: #00ff41 !important;
}

.product-left {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  gap: 8px !important;
  flex: 1 !important;
}

.product-right {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end !important;
}

.product-name-bigger {
  font-size: 1.8rem !important; /* Much bigger font */
  font-weight: bold !important;
  color: #00ff41 !important;
  text-shadow: 0 0 20px rgba(0, 255, 65, 0.9) !important;
  margin-bottom: 8px !important;
  line-height: 1.3 !important;
  max-width: 100% !important;
  word-wrap: break-word !important;
}

.product-price-right {
  font-size: 2rem !important;
  font-weight: bold !important;
  color: #00ff41 !important;
  background: rgba(0, 255, 65, 0.15) !important;
  padding: 12px 20px !important;
  border-radius: 8px !important;
  text-shadow: 0 0 20px rgba(0, 255, 65, 0.8) !important;
  border: 2px solid rgba(0, 255, 65, 0.3) !important;
  min-width: 150px !important;
  text-align: center !important;
}

/* Stock in Left Side - NO BARCODE DISPLAY */
.product-left .product-stock {
  font-size: 0.9rem !important;
  color: #7fb3d3 !important;
  background: rgba(127, 179, 211, 0.1) !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  border: 1px solid rgba(127, 179, 211, 0.2) !important;
  white-space: nowrap !important;
  display: inline-block !important;
}

/* Hide barcode display completely */
.product-left .product-barcode {
  display: none !important;
}

/* Arabic Language Support - Reverse Layout */
.lang-ar .product-info-layout {
  flex-direction: row-reverse !important;
}

.lang-ar .product-left {
  align-items: flex-end !important;
  text-align: right !important;
}

.lang-ar .product-right {
  justify-content: flex-start !important;
}

/* Edit Invoice Modal - Enhanced LCD Display Like Photo */
.large-modal .lcd-display-row .lcd-screen {
  background: linear-gradient(135deg, #0f1419, #1a252f);
  border: 3px solid #00ff41;
  border-radius: 8px;
  padding: 1rem;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.6);
  position: relative;
  overflow: hidden;
  width: 100%;
}

/* Edit Invoice Modal - Enhanced Product Display */
.large-modal .lcd-display-row .product-info {
  text-align: center;
  color: #00ff41;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.large-modal .lcd-display-row .product-name {
  font-size: 1.1rem; /* Smaller font like photo */
  font-weight: bold;
  color: #00ff41;
  text-shadow: 0 0 10px rgba(0, 255, 65, 0.8);
  margin-bottom: 8px; /* Smaller margin like photo */
}

.large-modal .lcd-display-row .product-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px; /* Smaller gap like photo */
  width: 100%;
}

.large-modal .lcd-display-row .product-price-big {
  font-size: 1.4rem; /* Smaller font like photo */
  font-weight: bold;
  color: #00ff41;
  background: rgba(0, 255, 65, 0.15);
  padding: 8px 12px; /* Smaller padding like photo */
  border-radius: 6px; /* Smaller radius like photo */
  display: inline-block;
  min-width: 120px; /* Smaller width like photo */
  text-shadow: 0 0 15px rgba(0, 255, 65, 0.8);
  border: 2px solid rgba(0, 255, 65, 0.3);
}

.large-modal .lcd-display-row .product-stock,
.large-modal .lcd-display-row .product-barcode {
  font-size: 0.9rem; /* Smaller font like photo */
  color: #7fb3d3;
  background: rgba(127, 179, 211, 0.1);
  padding: 6px 10px; /* Smaller padding like photo */
  border-radius: 4px; /* Smaller radius like photo */
  border: 2px solid rgba(127, 179, 211, 0.2);
  white-space: nowrap;
}

/* Edit Invoice Modal - Enhanced No Product State Like Photo */
.large-modal .lcd-display-row .no-product {
  text-align: center;
  color: #7fb3d3;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.large-modal .lcd-display-row .waiting-text {
  font-size: 1rem; /* Smaller font like photo */
  font-style: italic;
  max-width: 90%;
  color: #7fb3d3;
  text-shadow: 0 0 8px rgba(127, 179, 211, 0.5);
}

.large-modal .lcd-display-row .scan-icon {
  font-size: 2.5rem; /* Smaller icon like photo */
  opacity: 0.5;
  animation: pulse 2s infinite;
  color: #7fb3d3;
}

.lcd-display-row .product-info {
  text-align: center;
  color: #00ff41;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px; /* Much larger gap */
}

.lcd-display-row .product-name {
  font-size: 1.6rem; /* Much larger font for clarity */
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5);
  margin-bottom: 15px; /* Much larger margin */
  line-height: 1.4; /* Better line height */
  max-width: 90%; /* Prevent overflow */
  word-wrap: break-word; /* Handle long names */
}

.lcd-display-row .product-details {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 25px; /* Much larger gap */
  flex-wrap: wrap;
  width: 100%;
}

.lcd-display-row .product-price-big {
  font-size: 2rem; /* Much larger font for better visibility */
  font-weight: bold;
  color: #00ff41;
  background: rgba(0, 255, 65, 0.15);
  padding: 15px 20px; /* Much larger padding */
  border-radius: 10px; /* Larger border radius */
  display: inline-block;
  min-width: 180px; /* Much wider for better visibility */
  text-shadow: 0 0 20px rgba(0, 255, 65, 0.8);
  border: 2px solid rgba(0, 255, 65, 0.3);
}

.lcd-display-row .product-stock,
.lcd-display-row .product-barcode {
  font-size: 1.2rem; /* Much larger font for clarity */
  color: #7fb3d3;
  background: rgba(127, 179, 211, 0.1);
  padding: 10px 15px; /* Much larger padding */
  border-radius: 8px; /* Larger border radius */
  border: 2px solid rgba(127, 179, 211, 0.2);
  white-space: nowrap; /* Prevent text wrapping */
}

.lcd-display-row .no-product {
  text-align: center;
  color: #7fb3d3;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px; /* Much larger gap */
}

.lcd-display-row .waiting-text {
  font-size: 1.4rem; /* Much larger font for clarity */
  font-style: italic;
  max-width: 90%; /* Prevent overflow */
}

.lcd-display-row .scan-icon {
  font-size: 3.5rem; /* Much larger icon */
  opacity: 0.5;
  animation: pulse 2s infinite;
}

/* Responsive Design for Smaller Sales Scanner */
@media (max-width: 768px) {
  .sales-barcode-scanner-row {
    margin: 0.3rem 0; /* Smaller margin */
    grid-template-columns: 1fr !important;
    grid-template-areas: "scanner" "lcd" !important;
    min-height: 200px; /* Smaller mobile height */
    max-width: 100%; /* Full width on mobile */
  }

  .scanner-input-row,
  .lcd-display-row {
    padding: 0.8rem; /* Smaller padding */
    border-right: none !important;
    border-left: none !important;
  }

  .scanner-input-row {
    border-bottom: 2px solid #1a252f;
  }

  .scanner-input-row .barcode-input {
    font-size: 14px; /* Smaller mobile font */
    padding: 8px 10px 8px 30px; /* Smaller mobile padding */
    min-height: 35px; /* Smaller mobile height */
  }

  .scanner-input-row .barcode-icon {
    left: 8px; /* Smaller mobile positioning */
    font-size: 14px; /* Smaller mobile icon */
  }

  .lang-ar .scanner-input-row .barcode-icon {
    right: 8px; /* Smaller mobile positioning for Arabic */
  }

  .lang-ar .scanner-input-row .barcode-input {
    padding: 8px 30px 8px 10px; /* Smaller mobile padding for Arabic */
  }

  .scanner-input-row .barcode-input-container .btn {
    padding: 8px 10px; /* Smaller mobile button */
    font-size: 10px; /* Smaller mobile font */
    min-height: 35px; /* Match smaller input height */
  }

  .lcd-display-row .lcd-screen {
    min-height: 60px; /* Smaller mobile LCD height */
    padding: 0.6rem; /* Smaller mobile padding */
  }

  .lcd-display-row .product-details {
    flex-direction: column;
    gap: 4px; /* Smaller mobile gap */
  }

  .lcd-display-row .product-price-big {
    font-size: 1rem; /* Smaller mobile font */
    min-width: 70px; /* Smaller mobile width */
    padding: 3px 6px; /* Smaller mobile padding */
  }

  .lcd-display-row .product-name {
    font-size: 0.8rem; /* Smaller mobile font */
  }

  .lcd-display-row .product-stock,
  .lcd-display-row .product-barcode {
    font-size: 0.7rem; /* Smaller mobile font */
    padding: 2px 4px; /* Smaller mobile padding */
  }
}

.product-selection-container .product-search {
  padding: 8px 12px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.product-selection-container .product-search:focus {
  outline: none;
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 2px rgba(22, 160, 133, 0.1);
}

.product-selection-container select {
  max-height: 120px;
  overflow-y: auto;
}

.btn-add {
  padding: 12px 20px;
  font-weight: 700;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  color: white;
  font-size: 1rem;
  box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
}

.btn-add:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
}

.btn-add:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

/* Invoice Items Landscape */
.invoice-items-landscape {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  padding: 15px;
  overflow: hidden;
  border: 2px solid #dee2e6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.invoice-items-landscape h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.no-items-landscape {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #6c757d;
  font-style: italic;
}

.no-items-landscape p {
  margin: 5px 0;
  font-size: 1.1rem;
}

.items-table-container {
  flex: 1;
  overflow-y: auto;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.items-table-landscape {
  width: 100%;
  border-collapse: collapse;
  background: white;
  font-size: 0.9rem;
}

.items-table-landscape th {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.items-table-landscape td {
  padding: 12px 8px;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
  transition: background-color 0.3s ease;
}

.items-table-landscape tbody tr:hover {
  background-color: #f1f3f4;
}

.items-table-landscape tbody tr:nth-child(even) {
  background-color: #fafafa;
}

.items-table-landscape .product-name {
  text-align: right;
  font-weight: 600;
  color: #2c3e50;
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
}

.items-table-landscape .quantity {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  font-weight: 700;
  color: #1976d2;
  font-size: 1.1rem;
}

.items-table-landscape .price {
  font-weight: 600;
  color: #ff6f00;
  background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
}

.items-table-landscape .total {
  font-weight: 700;
  color: #2e7d32;
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
  font-size: 1.1rem;
}

.btn-icon-delete {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-icon-delete:hover {
  background: #c82333;
  transform: scale(1.1);
}

/* Invoice Totals Landscape */
.invoice-totals-landscape {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #e9ecef;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.discount-section {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.discount-section label {
  font-weight: 600;
  color: #495057;
  font-size: 1.1rem;
}

.discount-input-large {
  flex: 1;
  padding: 10px 15px;
  border: 2px solid #ffc107;
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
  background: #fff8e1;
}

.discount-input-large:focus {
  border-color: #ff8f00;
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
}

.totals-summary-landscape {
  margin-bottom: 20px;
}

.totals-summary-landscape .total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 1rem;
}

.totals-summary-landscape .total-row:last-child {
  border-bottom: none;
}

.totals-summary-landscape .amount {
  font-weight: 600;
  color: #2e7d32;
}

.totals-summary-landscape .amount.discount {
  color: #d32f2f;
}

.totals-summary-landscape .final-total {
  background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
  color: white;
  padding: 12px;
  border-radius: 6px;
  margin-top: 10px;
  font-size: 1.2rem;
  font-weight: 700;
}

.totals-summary-landscape .final-total .amount.final {
  color: white;
  font-size: 1.3rem;
}

.action-buttons-landscape {
  display: flex;
  gap: 15px;
}

.btn-save {
  flex: 1;
  padding: 15px 20px;
  font-size: 1.2rem;
  font-weight: 700;
  border-radius: 10px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-cancel {
  flex: 1;
  padding: 15px 20px;
  font-size: 1.2rem;
  font-weight: 700;
  border-radius: 10px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-save:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
}

.btn-cancel:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
  background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
}

/* Responsive Design for Sales Modal */
@media (max-width: 1200px) {
  .sales-modal-landscape {
    width: 98vw;
    height: 98vh;
    margin: 1vh auto;
  }

  .left-panel {
    flex: 0 0 350px;
  }

  .form-grid-compact {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .product-selection-compact .form-grid-compact {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .modal-body-landscape {
    flex-direction: column;
    height: calc(98vh - 100px);
  }

  .left-panel {
    flex: none;
    max-height: 40vh;
    overflow-y: auto;
  }

  .right-panel {
    flex: 1;
    min-height: 0;
  }

  .product-selection-compact .form-grid-compact {
    grid-template-columns: 1fr;
  }

  .action-buttons-landscape {
    flex-direction: column;
  }

  .btn-save,
  .btn-cancel {
    padding: 15px;
    font-size: 1.2rem;
  }

  .items-table-landscape th,
  .items-table-landscape td {
    padding: 8px 4px;
    font-size: 0.8rem;
  }

  .invoice-items-landscape h3 {
    font-size: 1rem;
  }
}

/* Sales Invoice Styles - Single Screen Landscape */
.sales-invoice-landscape {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  overflow-y: auto;
}

/* Header Section */
.invoice-header-section {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.customer-info .form-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 15px;
  align-items: end;
}

/* Product Selection Section */
.product-selection-section {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.barcode-section {
  margin-bottom: 15px;
}

.barcode-section label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #333;
}

.barcode-section .barcode-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #28a745;
  border-radius: 6px;
  font-size: 16px;
  background: #f8fff9;
}

.manual-selection .form-row {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr auto;
  gap: 15px;
  align-items: end;
}

.form-group.flex-2 {
  grid-column: span 1;
}

/* Invoice Content Section */
.invoice-content-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 15px;
  flex: 1;
  overflow: visible;
  min-height: 400px;
}

.items-section {
  background: white;
  padding: 10px !important;
  border-radius: 5px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.items-section h3 {
  margin: 0 0 10px 0 !important;
  color: #333;
  font-size: 14px !important;
}

.no-items {
  text-align: center;
  padding: 20px !important;
  color: #666;
  background: #f8f9fa;
  border-radius: 3px;
  border: 2px dashed #ddd;
  font-size: 12px !important;
}

.items-table {
  flex: 1;
  overflow-y: auto;
}

.items-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 16px !important;
}

.items-table th {
  background: #f8f9fa;
  padding: 12px 8px !important;
  text-align: right;
  border-bottom: 2px solid #dee2e6;
  font-weight: 700;
  color: #333;
  position: sticky;
  top: 0;
  font-size: 16px !important;
}

.items-table td {
  padding: 12px 8px !important;
  border-bottom: 1px solid #dee2e6;
  text-align: right;
  font-size: 16px !important;
  font-weight: 600;
}

/* Enhanced styling for invoice items */
.items-table td:first-child {
  font-weight: 700 !important;
  color: #2c3e50 !important;
  font-size: 17px !important;
}

.items-table td:nth-child(2) {
  font-weight: 700 !important;
  color: #e74c3c !important;
  font-size: 16px !important;
}

.items-table td:nth-child(3) {
  font-weight: 700 !important;
  color: #3498db !important;
  font-size: 16px !important;
}

.items-table td:nth-child(4) {
  font-weight: 700 !important;
  color: #27ae60 !important;
  font-size: 17px !important;
}

.quantity-input {
  width: 60px !important;
  padding: 8px 6px !important;
  border: 2px solid #3498db;
  border-radius: 6px;
  text-align: center;
  font-size: 16px !important;
  font-weight: 700 !important;
  color: #2c3e50 !important;
  background: #ecf0f1 !important;
}

.btn-delete {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 8px 12px !important;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px !important;
  font-weight: 600 !important;
  min-width: auto !important;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-delete:hover {
  background: #c0392b !important;
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-delete:hover {
  background: #c82333;
}

/* Totals Section - Compact */
.totals-section {
  background: white;
  padding: 10px !important;
  border-radius: 5px;
  border: 1px solid #e0e0e0;
  display: flex !important;
  flex-direction: column !important;
  height: fit-content;
  min-height: 300px;
}

.totals-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.discount-input label {
  display: block;
  margin-bottom: 3px;
  font-weight: 600;
  color: #333;
  font-size: 12px !important;
}

.discount-input input {
  width: 100%;
  padding: 6px !important;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 12px !important;
}

.totals-summary {
  background: #f8f9fa;
  padding: 10px !important;
  border-radius: 3px;
  border: 1px solid #e0e0e0;
}

.total-row {
  display: flex;
  justify-content: space-between;
  padding: 4px 0 !important;
  border-bottom: 1px solid #dee2e6;
  font-size: 12px !important;
}

.total-row:last-child {
  border-bottom: none;
}

.total-row.final {
  font-weight: bold;
  font-size: 14px !important;
  color: #28a745;
  background: white;
  margin: 6px -10px -10px -10px;
  padding: 10px !important;
  border-radius: 0 0 3px 3px;
}

.action-buttons {
  display: flex;
  gap: 6px;
}

.action-buttons .btn {
  flex: 1;
  padding: 8px !important;
  font-size: 12px !important;
  font-weight: 600;
}

/* Responsive Design for New Layout */
@media (max-width: 1200px) {
  .invoice-content-section {
    grid-template-columns: 1fr;
  }

  .customer-info .form-row {
    grid-template-columns: 1fr 1fr;
    gap: 10px;
  }

  .manual-selection .form-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .sales-invoice-landscape {
    padding: 8px;
    gap: 8px;
  }

  .customer-info .form-row {
    grid-template-columns: 1fr;
  }

  .items-section {
    padding: 8px;
  }

  .items-section h3 {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .totals-section {
    padding: 8px;
  }

  .action-buttons .btn {
    padding: 8px;
    font-size: 12px;
  }

  .btn-large {
    padding: 6px 12px;
    font-size: 0.85rem;
    min-width: 100px;
  }

  .table-actions {
    padding: 1rem;
    gap: 0.6rem;
  }

  .no-items {
    padding: 20px;
    font-size: 12px;
  }
}

/* Right Side Action Buttons - REMOVED - Only keeping scroll-to-top */

/* Left Side Action Buttons - REMOVED */

/* Button styling removed - no longer needed */

/* Tooltip styles removed - no longer needed */

/* Supplier Selection Controls */
.supplier-selection {
  display: flex;
  gap: 10px;
  align-items: center;
}

.supplier-selection select {
  flex: 1;
}

.supplier-selection .btn {
  white-space: nowrap;
  padding: 8px 12px;
  font-size: 12px;
}

/* Product Selection Controls */
.product-selection-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.product-selection-controls select {
  flex: 1;
}

.product-selection-controls .btn {
  white-space: nowrap;
  padding: 8px 12px;
  font-size: 12px;
}

/* Large Modal */
.large-modal {
  max-width: 900px !important;
  width: 90% !important;
}

/* Suppliers Management */
.suppliers-management {
  padding: 20px 0;
}

.suppliers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.suppliers-table-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.suppliers-table-container .data-table {
  margin: 0;
}

.suppliers-table-container .data-table th {
  position: sticky;
  top: 0;
  background: var(--primary-color);
  color: white;
  z-index: 10;
}

/* Form Grid Full Width */
.form-grid .form-group.full-width {
  grid-column: 1 / -1;
}

/* 🖨️ ENHANCED THERMAL PRINT STYLES - 80mm with Arabic RTL Support */
@media print {
  @page {
    size: 80mm auto;
    margin: 0;
  }

  body {
    font-family: 'Courier New', monospace !important;
    font-size: 14px !important;
    font-weight: bold !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding: 3mm !important;
    width: 74mm !important;
    color: black !important;
    background: white !important;
    text-align: center !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* RTL Support for Arabic */
  body[dir="rtl"] {
    direction: rtl !important;
    text-align: right !important;
  }

  body[dir="ltr"] {
    direction: ltr !important;
    text-align: left !important;
  }

  * {
    color: black !important;
    background: white !important;
    font-weight: bold !important;
  }

  /* Enhanced thermal print elements */
  .thermal-header {
    font-size: 18px !important;
    font-weight: bold !important;
    text-transform: uppercase !important;
    margin-bottom: 4mm !important;
    border-bottom: 2px solid black !important;
    padding-bottom: 2mm !important;
    text-align: center !important;
  }

  .thermal-info {
    font-size: 12px !important;
    margin: 2mm 0 !important;
    text-align: left !important;
  }

  .thermal-info[dir="rtl"] {
    text-align: right !important;
  }

  .thermal-table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin: 3mm 0 !important;
    font-size: 11px !important;
  }

  .thermal-table th,
  .thermal-table td {
    padding: 1mm !important;
    text-align: left !important;
    border-bottom: 1px solid black !important;
  }

  .thermal-table[dir="rtl"] th,
  .thermal-table[dir="rtl"] td {
    text-align: right !important;
  }

  .thermal-table th {
    background: black !important;
    color: white !important;
    font-weight: bold !important;
  }

  .thermal-text {
    font-size: 14px !important;
    font-weight: bold !important;
    margin: 2mm 0 !important;
  }

  .thermal-total {
    font-size: 16px !important;
    font-weight: bold !important;
    border: 3px double black !important;
    padding: 3mm !important;
    margin: 3mm 0 !important;
    background: #f0f0f0 !important;
    text-align: center !important;
  }

  .thermal-footer {
    font-size: 10px !important;
    font-weight: bold !important;
    margin-top: 4mm !important;
    border-top: 2px solid black !important;
    padding-top: 3mm !important;
    text-align: center !important;
  }

  /* Separator lines */
  .separator {
    border-top: 1px dashed black !important;
    margin: 2mm 0 !important;
    height: 1px !important;
  }

  /* Center alignment utilities */
  .center {
    text-align: center !important;
  }

  .right {
    text-align: right !important;
  }

  .left {
    text-align: left !important;
  }

  .bold {
    font-weight: bold !important;
  }

  /* Hide non-print elements */
  .no-print {
    display: none !important;
  }

  /* Developer footer styling */
  .developer-footer {
    margin-top: 4mm !important;
    padding-top: 2mm !important;
    border-top: 1px dashed black !important;
    font-size: 10px !important;
    font-weight: bold !important;
    text-align: center !important;
  }

  .developer-name {
    margin-bottom: 1mm !important;
  }

  .developer-phone {
    font-size: 12px !important;
    font-weight: bold !important;
  }
}

/* 🖨️ DIRECT PRINTING SUPPORT - Non-print media styles */
.thermal-receipt {
  max-width: 80mm;
  margin: 0 auto;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.4;
  color: black;
  background: white;
  padding: 3mm;
}

.thermal-receipt[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

.thermal-receipt[dir="ltr"] {
  direction: ltr;
  text-align: left;
}

/* Direct printing iframe styles */
.direct-print-iframe {
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  width: 1px !important;
  height: 1px !important;
  border: none !important;
  visibility: hidden !important;
}

/* Print status indicators */
.print-status {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  font-size: 12px;
  z-index: 10000;
  display: none;
}

.print-status.show {
  display: block;
}

.print-status.success {
  background: rgba(40, 167, 69, 0.9);
}

.print-status.error {
  background: rgba(220, 53, 69, 0.9);
}

.print-status.info {
  background: rgba(23, 162, 184, 0.9);
}

/* Button spacing for action buttons */
.btn-sm {
  margin: 0 2px;
  padding: 4px 8px;
  font-size: 11px;
}

/* Bulk Selection Styles */
.bulk-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.bulk-actions.has-selection {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border-color: #ffc107;
}

.select-all-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.select-all-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #007bff;
}

.select-all-label {
  font-weight: 600;
  color: #495057;
  cursor: pointer;
  user-select: none;
}

.selected-count {
  color: #6c757d;
  font-size: 14px;
  margin-left: 10px;
}

.bulk-delete-btn {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.bulk-delete-btn:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.bulk-delete-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Row Selection Styles */
.row-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: #007bff;
}

.data-table tr.selected {
  background: rgba(0, 123, 255, 0.1) !important;
  border-left: 3px solid #007bff;
}

.data-table tr.selected:hover {
  background: rgba(0, 123, 255, 0.15) !important;
}

/* Checkbox column styling */
.checkbox-column {
  width: 50px;
  text-align: center !important;
  padding: 8px !important;
}

/* French and English checkbox alignment */
.lang-fr .checkbox-column,
.lang-en .checkbox-column {
  text-align: center !important;
}

/* Header Actions */
.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.header-actions .btn {
  white-space: nowrap;
}

/* Print Report Button */
.btn-info {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.btn-info:hover {
  background: linear-gradient(135deg, #138496, #117a8b);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

/* Responsive adjustments - removed unused button styles */

/* Responsive adjustments for removed buttons - cleaned up */
@media (max-width: 768px) {
  /* Responsive supplier/product controls */
  .supplier-selection,
  .product-selection-controls {
    flex-direction: column;
    gap: 8px;
  }

  .supplier-selection select,
  .product-selection-controls select {
    width: 100%;
  }

  .supplier-selection .btn,
  .product-selection-controls .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Mobile responsive styles - removed unused button styles */

/* Force Compact Styles - Override any conflicting styles */
.sales-modal-landscape .btn,
.modal-content .btn {
  padding: 0.5rem 1rem !important;
  font-size: 0.8rem !important;
  min-width: 100px !important;
}

.sales-modal-landscape .btn-large {
  padding: 6px 12px !important;
  font-size: 0.85rem !important;
  min-width: 100px !important;
}

.sales-modal-landscape .items-table th,
.sales-modal-landscape .items-table td {
  padding: 12px 8px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
}

.sales-modal-landscape .quantity-input {
  width: 60px !important;
  padding: 8px 6px !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  color: #2c3e50 !important;
  background: #ecf0f1 !important;
  border: 2px solid #3498db !important;
}

.sales-modal-landscape .btn-delete {
  padding: 8px 12px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  min-width: auto !important;
}

.sales-modal-landscape .total-row {
  padding: 4px 0 !important;
  font-size: 12px !important;
}

.sales-modal-landscape .action-buttons .btn {
  padding: 12px 20px !important;
  font-size: 14px !important;
  min-height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Make sure action buttons are visible and properly sized */
.action-buttons {
  display: flex !important;
  gap: 10px !important;
  margin-top: 15px !important;
  padding: 10px 0 !important;
}

.action-buttons .btn {
  flex: 1 !important;
  padding: 12px 20px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  min-height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Specific styles for sales modal buttons */
.sales-modal-landscape .totals-section .action-buttons,
.modal-content .totals-section .action-buttons {
  display: flex !important;
  gap: 10px !important;
  margin-top: 15px !important;
  padding: 10px 0 !important;
  width: 100% !important;
}

.sales-modal-landscape .totals-section .action-buttons .btn,
.modal-content .totals-section .action-buttons .btn {
  flex: 1 !important;
  padding: 12px 20px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  min-height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 6px !important;
  cursor: pointer !important;
}

/* Force visibility of action buttons */
.totals-section .action-buttons {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 999 !important;
}

.totals-section .action-buttons .btn {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 999 !important;
}

/* Purchase Modal Specific Styles */
.purchase-modal-body .invoice-header-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.purchase-modal-body .product-selection-section {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.purchase-modal-body .invoice-content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.purchase-modal-body .items-section {
  flex: 1;
  min-height: 200px;
}

.purchase-modal-body .totals-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.purchase-modal-body .no-items {
  text-align: center;
  padding: 40px;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.purchase-modal-body .items-table {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.purchase-modal-body .totals-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  align-items: start;
}

.purchase-modal-body .discount-input {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.purchase-modal-body .totals-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.purchase-modal-body .action-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* Simplified Purchase Modal Totals Section */
.totals-section-simple {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-top: 20px;
}

.simple-total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 2px solid #007bff;
  margin-bottom: 20px;
}

.total-label {
  font-size: 18px;
  font-weight: bold;
  color: #495057;
}

.total-value {
  font-size: 20px;
  font-weight: bold;
  color: #007bff;
}

.action-buttons-simple {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.action-buttons-simple .btn {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 6px;
  min-width: 150px;
}

/* Force action buttons to be at the bottom and visible */
.sales-modal-landscape .totals-section {
  position: relative !important;
  height: auto !important;
  min-height: 350px !important;
}

.sales-modal-landscape .totals-grid {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
}

.sales-modal-landscape .action-buttons {
  margin-top: auto !important;
  padding: 15px 0 !important;
  background: white !important;
  border-top: 1px solid #e0e0e0 !important;
  position: sticky !important;
  bottom: 0 !important;
}

/* Additional specific styles for buttons */
button.btn.btn-primary,
button.btn.btn-secondary {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  min-height: 40px !important;
  padding: 12px 20px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  border-radius: 6px !important;
  cursor: pointer !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

/* Ensure buttons are not hidden by overflow */
.totals-section {
  overflow: visible !important;
}

.totals-grid {
  overflow: visible !important;
}

/* Reports Page Styles */
.reports-page {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.reports-page .page-header {
  text-align: center;
  margin-bottom: 40px;
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.reports-page .page-header h1 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 32px;
  font-weight: bold;
}

.reports-page .page-header p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
}

.reports-categories {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.report-category {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.report-category:hover {
  transform: translateY(-2px);
}

.category-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #ecf0f1;
}

.category-header h2 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: bold;
}

.category-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 14px;
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.report-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  overflow: hidden;
}

.report-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.report-card:hover::before {
  transform: scaleX(1);
}

.report-card:hover {
  background: white;
  border-color: #3498db;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
}

.report-icon {
  font-size: 32px;
  min-width: 50px;
  text-align: center;
}

.report-info {
  flex: 1;
}

.report-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: bold;
}

.report-info p {
  margin: 0 0 12px 0;
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.4;
}

.report-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.report-stats span {
  font-size: 16px;
  font-weight: bold;
  color: #27ae60;
}

.report-stats small {
  font-size: 12px;
  color: #95a5a6;
}

.report-action {
  font-size: 24px;
  color: #3498db;
  transition: transform 0.3s ease;
}

.report-card:hover .report-action {
  transform: scale(1.2);
}

.reports-summary {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-top: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.reports-summary h2 {
  text-align: center;
  color: #2c3e50;
  margin: 0 0 30px 0;
  font-size: 24px;
  font-weight: bold;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: #ecf0f1;
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 32px;
  min-width: 50px;
  text-align: center;
}

.stat-details h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.stat-details p {
  margin: 0;
  color: #27ae60;
  font-size: 18px;
  font-weight: bold;
}

/* Responsive Design for Reports */
@media (max-width: 768px) {
  .reports-page {
    padding: 15px;
  }

  .reports-page .page-header {
    padding: 20px;
  }

  .reports-page .page-header h1 {
    font-size: 24px;
  }

  .report-category {
    padding: 20px;
  }

  .category-header h2 {
    font-size: 20px;
  }

  .reports-grid {
    grid-template-columns: 1fr;
  }

  .report-card {
    padding: 20px;
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .stat-item {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
}

/* FORCE BUTTON STYLES - HIGHEST PRIORITY */
table .action-buttons-group button,
.data-table .action-buttons-group button,
.inventory-table .action-buttons-group button,
td .action-buttons-group button,
.action-buttons-group .btn.btn-xs,
.action-buttons-group button.btn-xs {
  padding: 0 !important;
  margin: 0 !important;
  min-width: 32px !important;
  max-width: 32px !important;
  width: 32px !important;
  height: 32px !important;
  font-size: 14px !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0 !important;
  text-align: center !important;
  line-height: 1 !important;
  border: none !important;
  box-sizing: border-box !important;
}

/* Admin Permission Styles */
.vendor-restricted {
  color: #6c757d;
  font-size: 12px;
  font-style: italic;
  text-align: center;
  padding: 8px;
}

.stock-display-vendor {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.stock-value {
  font-weight: 600;
  color: #495057;
}

.access-denied-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.access-denied-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.access-denied-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.7;
}

.access-denied-content h1 {
  color: #dc3545;
  margin-bottom: 15px;
  font-size: 1.8rem;
}

.access-denied-content p {
  color: #6c757d;
  margin-bottom: 15px;
  line-height: 1.6;
}

.access-denied-content .btn {
  margin-top: 20px;
  padding: 12px 24px;
}

/* Advanced Reports Styling */
.advanced-card {
  border: 2px solid #3498db !important;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
}

.advanced-card .report-icon {
  color: #3498db;
}

.advanced-card .report-info h3 {
  color: #1976d2;
  margin-bottom: 10px;
}

.advanced-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.report-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.date-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-selector label {
  font-size: 12px;
  font-weight: 500;
  color: #1976d2;
  min-width: 70px;
}

.date-selector select,
.date-selector input {
  padding: 4px 8px;
  border: 1px solid #3498db;
  border-radius: 4px;
  font-size: 12px;
  background: white;
}

.advanced-report-display {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin: 20px 0;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.advanced-report-display h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.report-date {
  color: #7f8c8d;
  margin-bottom: 20px;
  font-size: 14px;
}

.report-summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.best-products-section {
  margin-top: 25px;
}

.best-products-section h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.products-table-container {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.products-table {
  width: 100%;
  border-collapse: collapse;
}

.products-table th {
  background: #3498db;
  color: white;
  padding: 12px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
}

.products-table td {
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid #ecf0f1;
  font-size: 13px;
}

.products-table tr:nth-child(even) {
  background: #f8f9fa;
}

.products-table tr:hover {
  background: #e3f2fd;
}

.best-product {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%) !important;
  font-weight: 600;
}

.best-product:hover {
  background: linear-gradient(135deg, #ffecb3 0%, #ffd54f 100%) !important;
}

.crown {
  margin-left: 5px;
  font-size: 16px;
}

.profit-positive {
  color: #27ae60;
  font-weight: 600;
}

.profit-negative {
  color: #e74c3c;
  font-weight: 600;
}

/* Language-specific layout adjustments for Reports */
/* French and English LTR Layout */
.lang-fr .reports-page,
.lang-en .reports-page {
  direction: ltr;
  text-align: left;
}

/* MAIN REPORTS PAGE HEADER - TITLE LEFT ALIGNED FOR FR/EN */
.lang-fr .reports-page .page-header,
.lang-en .reports-page .page-header {
  text-align: left !important;
}

.lang-fr .reports-page .page-header h1,
.lang-en .reports-page .page-header h1 {
  text-align: left !important;
}

.lang-fr .reports-page .page-header p,
.lang-en .reports-page .page-header p {
  text-align: left !important;
}

/* Generic page headers for other pages */
.lang-fr .page-header,
.lang-en .page-header {
  text-align: left;
}

.lang-fr .page-header h1,
.lang-en .page-header h1 {
  text-align: left;
}

.lang-fr .page-header p,
.lang-en .page-header p {
  text-align: left;
}

/* Ensure Arabic maintains center alignment for reports page header */
.lang-ar .reports-page .page-header {
  text-align: center !important;
}

.lang-ar .reports-page .page-header h1 {
  text-align: center !important;
}

.lang-ar .reports-page .page-header p {
  text-align: center !important;
}

.lang-fr .category-header,
.lang-en .category-header {
  text-align: left;
}

.lang-fr .category-header h2,
.lang-en .category-header h2 {
  text-align: left;
}

.lang-fr .category-header p,
.lang-en .category-header p {
  text-align: left;
}

.lang-fr .report-card,
.lang-en .report-card {
  text-align: left;
}

.lang-fr .report-info,
.lang-en .report-info {
  text-align: left;
}

.lang-fr .report-info h3,
.lang-en .report-info h3 {
  text-align: left;
}

.lang-fr .report-info p,
.lang-en .report-info p {
  text-align: left;
}

.lang-fr .report-stats,
.lang-en .report-stats {
  text-align: left;
}

.lang-fr .reports-summary h2,
.lang-en .reports-summary h2 {
  text-align: center;
}

.lang-fr .stat-details,
.lang-en .stat-details {
  text-align: center;
}

.lang-fr .stat-details h3,
.lang-en .stat-details h3 {
  text-align: center;
}

.lang-fr .stat-details p,
.lang-en .stat-details p {
  text-align: center;
}

/* Advanced Reports LTR Layout */
.lang-fr .advanced-report-display,
.lang-en .advanced-report-display {
  text-align: left;
}

.lang-fr .advanced-report-display h2,
.lang-en .advanced-report-display h2 {
  text-align: left;
}

.lang-fr .report-date,
.lang-en .report-date {
  text-align: left;
}

.lang-fr .best-products-section h3,
.lang-en .best-products-section h3 {
  text-align: left;
}

/* Summary Cards Center Alignment for FR/EN */
.lang-fr .summary-card,
.lang-en .summary-card {
  text-align: center;
}

.lang-fr .summary-card h3,
.lang-en .summary-card h3 {
  text-align: center;
}

.lang-fr .summary-value,
.lang-en .summary-value {
  text-align: center;
}

/* Date display adjustments for LTR */
.lang-fr .report-stats span,
.lang-en .report-stats span {
  text-align: left;
}

.lang-fr .report-stats small,
.lang-en .report-stats small {
  text-align: left;
}







/* Scanner Title Styles */
.scanner-title {
  margin: 0 0 10px 0;
  color: #28a745;
  font-size: 1rem;
  font-weight: bold;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Edit Invoice Layout for FR/EN - LCD Left, Scanner Right */
.edit-invoice-layout-ltr {
  display: flex;
  flex-direction: row;
  gap: 20px;
  align-items: flex-start;
}

.edit-invoice-layout-ltr .lcd-display-row.lcd-left {
  flex: 1;
  order: 1;
}

.edit-invoice-layout-ltr .scanner-input-row.scanner-right {
  flex: 1;
  order: 2;
}

/* 💰 TOTAL FINAL DISPLAY STYLES - NEW LCD CONTENT */
.total-final-display {
  text-align: center;
  color: #00ff41;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 20px;
}

.total-final-label {
  font-size: 1.2rem;
  font-weight: bold;
  color: #7fb3d3;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.total-final-amount {
  font-size: 2.5rem;
  font-weight: bold;
  color: #00ff41;
  text-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
  font-family: 'Courier New', monospace;
}

.total-final-currency {
  font-size: 1rem;
  color: #7fb3d3;
  font-weight: bold;
  margin-top: -10px;
}

/* Dashboard LCD Invoice Info */
.invoice-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 10px;
}

.invoice-number {
  font-size: 0.9rem;
  color: #7fb3d3;
  font-weight: bold;
}

.timestamp {
  font-size: 0.8rem;
  color: #5a8aa8;
}

/* LCD Remise Section */
.lcd-remise-section {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 2px solid rgba(0, 255, 0, 0.3);
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.remise-label {
  font-size: 12px;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  color: #00ff00;
}

.lcd-remise-input {
  background: rgba(0, 255, 0, 0.1);
  border: 2px solid rgba(0, 255, 0, 0.3);
  border-radius: 6px;
  padding: 8px 12px;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  outline: none;
  text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

.lcd-remise-input:focus {
  border-color: #00ff00;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}

.lcd-remise-input::placeholder {
  color: rgba(0, 255, 0, 0.5);
}

.remise-currency {
  font-size: 12px;
  opacity: 0.7;
  font-weight: 600;
  margin-top: 5px;
  color: #00ff00;
}

/* Side by Side Layout - Fixed Height */
.side-by-side-layout {
  display: flex;
  gap: 20px;
  flex: 1;
  height: 500px;
  max-height: 500px;
  overflow: hidden;
}

.left-table-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  overflow: hidden;
  height: 100%;
}

.right-payment-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  overflow: hidden;
}

/* Table Header Controls */
.table-header-controls {
  background: #f8f9fa;
  padding: 15px;
  border-bottom: 1px solid #dee2e6;
}

.product-selection-row {
  display: flex;
  gap: 15px;
  align-items: end;
}

.select-product-col {
  flex: 2;
}

.quantity-col {
  flex: 1;
}

.add-button-col {
  flex: 0 0 auto;
}

.select-product-col label,
.quantity-col label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #495057;
}

.select-product-col select,
.quantity-col input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.add-button-col button {
  padding: 8px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-button-col button:hover:not(:disabled) {
  background: #218838;
}

.add-button-col button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* Items Table */
.items-table-container {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.no-items {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.invoice-table {
  width: 100%;
  border-collapse: collapse;
}

.invoice-table th {
  background: #f8f9fa;
  padding: 12px 8px;
  text-align: left;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
}

.invoice-table td {
  padding: 10px 8px;
  border-bottom: 1px solid #dee2e6;
}

.invoice-table tr:hover {
  background: #f8f9fa;
}

.quantity-input {
  width: 60px;
  padding: 4px 6px;
  border: 1px solid #ced4da;
  border-radius: 3px;
  text-align: center;
}

.btn-delete {
  background: #dc3545;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.btn-delete:hover {
  background: #c82333;
}

/* Payment Section */
.right-payment-section h3 {
  margin: 0 0 20px 0;
  color: #495057;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 10px;
}

.payment-field {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.payment-field label {
  font-weight: 600;
  color: #495057;
}

.payment-field select,
.payment-field input {
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.payment-field select:focus,
.payment-field input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Display Info */
.display-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  font-size: 14px;
}

.info-row span:first-child {
  color: #495057;
  font-weight: 500;
}

.info-row span:last-child {
  color: #007bff;
  font-weight: 600;
}

/* Payment Field with Button Next to It */
.payment-field-with-button {
  display: flex;
  gap: 20px;
  align-items: end;
  margin-bottom: 15px;
}

.field-part {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.field-part label {
  font-weight: 600;
  color: #495057;
}

.field-part select {
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  height: 42px;
  width: 100%;
}

.field-part select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-next-to-field {
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  height: 42px;
  flex: 1;
  min-width: 120px;
}

.btn-primary.btn-next-to-field {
  background: #007bff;
  color: white;
}

.btn-primary.btn-next-to-field:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn-secondary.btn-next-to-field {
  background: #6c757d;
  color: white;
}

.btn-secondary.btn-next-to-field:hover {
  background: #545b62;
  transform: translateY(-1px);
}

/* Paste Ticket Printing Styles */
.paste-ticket-buttons-section {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.paste-ticket-buttons-header {
  margin-bottom: 15px;
  text-align: center;
}

.paste-ticket-buttons-header h4 {
  color: #495057;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.paste-ticket-buttons-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.paste-ticket-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.paste-ticket-item-info {
  display: flex;
  flex-direction: column;
  gap: 3px;
  flex: 1;
}

.paste-ticket-item-info .product-name {
  font-weight: 600;
  color: #212529;
  font-size: 14px;
}

.paste-ticket-item-info .product-category {
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}

.paste-ticket-item-buttons {
  display: flex;
  gap: 8px;
}

.paste-ticket-item-buttons .btn {
  padding: 5px 10px;
  font-size: 11px;
  border-radius: 4px;
  min-width: 60px;
}

/* RTL Support for Paste Ticket Buttons */
.lang-ar .paste-ticket-item {
  direction: rtl;
}

.lang-ar .paste-ticket-item-info .product-name,
.lang-ar .paste-ticket-item-info .product-category {
  text-align: right;
}

/* LTR Support for Paste Ticket Buttons */
.lang-fr .paste-ticket-item,
.lang-en .paste-ticket-item {
  direction: ltr;
}

.lang-fr .paste-ticket-item-info .product-name,
.lang-en .paste-ticket-item-info .product-name,
.lang-fr .paste-ticket-item-info .product-category,
.lang-en .paste-ticket-item-info .product-category {
  text-align: left;
}

/* Product Modal Footer Paste Ticket Buttons */
.paste-ticket-modal-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.modal-action-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.paste-ticket-modal-buttons .btn {
  padding: 8px 12px;
  font-size: 13px;
  border-radius: 4px;
  min-width: 120px;
}

/* RTL Support for Modal Buttons */
.lang-ar .paste-ticket-modal-buttons {
  direction: rtl;
}

.lang-ar .modal-action-buttons {
  justify-content: flex-start;
}

/* LTR Support for Modal Buttons */
.lang-fr .paste-ticket-modal-buttons,
.lang-en .paste-ticket-modal-buttons {
  direction: ltr;
}

.lang-fr .modal-action-buttons,
.lang-en .modal-action-buttons {
  justify-content: flex-end;
}

/* Responsive */
@media (max-width: 1024px) {
  .side-by-side-layout {
    flex-direction: column;
  }

  .left-table-section,
  .right-payment-section {
    flex: none;
  }

  .product-selection-row {
    flex-direction: column;
    gap: 10px;
  }

  .select-product-col,
  .quantity-col,
  .add-button-col {
    flex: none;
  }
}