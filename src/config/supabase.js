/**
 * Supabase Configuration for iCalDZ Activation System
 *
 * This module provides the Supabase client configuration for the activation system.
 * It uses the credentials from the supabasekeys.md file.
 */

import { createClient } from '@supabase/supabase-js';

// Supabase configuration from dist/supabasekeys.md
const SUPABASE_URL = 'https://meaorrtisoruuoupldwq.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1lYW9ycnRpc29ydXVvdXBsZHdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNzQ3ODgsImV4cCI6MjA2Nzg1MDc4OH0.YERbS4mDoNhaxvF5fHNoQfE4bjvaxqptcN-11cSZjHM';

// Create Supabase client
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Export configuration for use in other modules
export const supabaseConfig = {
  url: SUPABASE_URL,
  anonKey: SUPABASE_ANON_KEY,
  projectId: 'meaorrtisoruuoupldwq'
};

// Table names
export const TABLES = {
  ACTIVATIONS: 'activations'
};

// Activation status constants
export const ACTIVATION_STATUS = {
  UNUSED: 'unused',
  ACTIVATED: 'activated',
  BLOCKED: 'blocked'
};

export { supabase };
export default supabase;
