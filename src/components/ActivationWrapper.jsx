/**
 * Activation Wrapper Component
 * 
 * This component wraps the main application and handles:
 * - Checking activation status on startup
 * - Showing activation dialog if needed
 * - Preventing app access without valid activation
 * - Managing activation state throughout the app lifecycle
 */

import React, { useState, useEffect, useContext } from 'react';
import { LanguageContext } from '../LanguageContext';
import { activationService } from '../services/ActivationService';
import ActivationDialog from './ActivationDialog';
import LicenseStatus from './LicenseStatus';
import './ActivationWrapper.css';

const ActivationWrapper = ({ children }) => {
  const { language, getTranslation } = useContext(LanguageContext);
  const [activationState, setActivationState] = useState({
    isLoading: true,
    isActivated: false,
    showActivationDialog: false,
    showLicenseStatus: false,
    error: null
  });

  useEffect(() => {
    initializeActivation();
  }, []);

  const t = (key, fallback) => getTranslation(key, language) || fallback;

  const initializeActivation = async () => {
    try {
      console.log('🔐 Initializing activation system...');
      
      // Initialize the activation service
      const initialized = await activationService.initialize();
      
      if (!initialized) {
        throw new Error('Failed to initialize activation service');
      }

      // Check activation status
      const status = activationService.getActivationStatus();
      
      if (status.isActivated) {
        console.log('✅ Application is activated');
        setActivationState({
          isLoading: false,
          isActivated: true,
          showActivationDialog: false,
          showLicenseStatus: false,
          error: null
        });
      } else {
        console.log('❌ Application is not activated');
        setActivationState({
          isLoading: false,
          isActivated: false,
          showActivationDialog: true,
          showLicenseStatus: false,
          error: null
        });
      }
    } catch (error) {
      console.error('❌ Failed to initialize activation:', error);
      setActivationState({
        isLoading: false,
        isActivated: false,
        showActivationDialog: true,
        showLicenseStatus: false,
        error: error.message
      });
    }
  };

  const handleActivationSuccess = (activationData) => {
    console.log('✅ Activation successful:', activationData);
    setActivationState({
      isLoading: false,
      isActivated: true,
      showActivationDialog: false,
      showLicenseStatus: false,
      error: null
    });
  };

  const handleShowLicenseStatus = () => {
    setActivationState(prev => ({
      ...prev,
      showLicenseStatus: true
    }));
  };

  const handleCloseLicenseStatus = () => {
    setActivationState(prev => ({
      ...prev,
      showLicenseStatus: false
    }));
  };

  const handleReactivate = () => {
    setActivationState(prev => ({
      ...prev,
      isActivated: false,
      showActivationDialog: true,
      showLicenseStatus: false
    }));
  };

  // Loading state
  if (activationState.isLoading) {
    return (
      <div className="activation-loading">
        <div className="loading-container">
          <div className="loading-logo">
            <img src="/logosvg.svg" alt="iCalDZ" className="logo-image" />
          </div>
          <div className="loading-spinner"></div>
          <h2 className="loading-title">
            {t('initializingApp', 'جاري تهيئة التطبيق...')}
          </h2>
          <p className="loading-subtitle">
            {t('checkingActivation', 'فحص حالة التفعيل...')}
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (activationState.error && !activationState.showActivationDialog) {
    return (
      <div className="activation-error">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h2 className="error-title">
            {t('activationError', 'خطأ في نظام التفعيل')}
          </h2>
          <p className="error-message">{activationState.error}</p>
          <button 
            className="btn-retry"
            onClick={initializeActivation}
          >
            {t('retry', 'إعادة المحاولة')}
          </button>
        </div>
      </div>
    );
  }

  // Not activated - show activation dialog
  if (!activationState.isActivated && activationState.showActivationDialog) {
    return (
      <ActivationDialog
        onActivationSuccess={handleActivationSuccess}
        onClose={() => {
          // Don't allow closing without activation
          console.log('Activation dialog close attempted - activation required');
        }}
      />
    );
  }

  // Activated - show main app with license status option
  if (activationState.isActivated) {
    return (
      <div className="activation-wrapper">
        {/* License Status Button */}
        <button 
          className="license-status-button"
          onClick={handleShowLicenseStatus}
          title={t('viewLicenseStatus', 'عرض حالة الترخيص')}
        >
          <span className="license-icon">🔐</span>
          <span className="license-text">{t('license', 'الترخيص')}</span>
        </button>

        {/* Main Application Content */}
        {children}

        {/* License Status Modal */}
        {activationState.showLicenseStatus && (
          <div className="license-status-modal">
            <div className="modal-overlay" onClick={handleCloseLicenseStatus}></div>
            <div className="modal-content">
              <div className="modal-header">
                <h3>{t('licenseInformation', 'معلومات الترخيص')}</h3>
                <button 
                  className="modal-close"
                  onClick={handleCloseLicenseStatus}
                >
                  ×
                </button>
              </div>
              <div className="modal-body">
                <LicenseStatus onReactivate={handleReactivate} />
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Fallback - should not reach here
  return (
    <div className="activation-fallback">
      <div className="fallback-container">
        <h2>{t('unexpectedError', 'خطأ غير متوقع')}</h2>
        <p>{t('contactSupport', 'يرجى التواصل مع الدعم الفني')}</p>
        <button onClick={initializeActivation}>
          {t('retry', 'إعادة المحاولة')}
        </button>
      </div>
    </div>
  );
};

export default ActivationWrapper;
