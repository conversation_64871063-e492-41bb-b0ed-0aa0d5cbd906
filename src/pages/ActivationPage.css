/* Modern Activation Page Styles */

.activation-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.activation-container {
  width: 100%;
  max-width: 500px;
  position: relative;
}

.activation-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Welcome Step */
.welcome-step {
  padding: 3rem 2rem;
  text-align: center;
}

.app-logo {
  margin-bottom: 2rem;
}

.logo-image {
  width: 100px;
  height: 100px;
  filter: drop-shadow(0 4px 20px rgba(0, 0, 0, 0.1));
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #2d3748;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 1.2rem;
  color: #4a5568;
  margin: 0 0 3rem 0;
  line-height: 1.6;
}

.features-list {
  margin: 2rem 0 3rem 0;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 1rem;
  padding: 1rem;
  margin: 0.5rem 0;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(102, 126, 234, 0.15);
  transform: translateX(5px);
}

.feature-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.feature-text {
  font-size: 1rem;
  color: #2d3748;
  font-weight: 500;
}

/* Code Entry Step */
.code-entry-step {
  padding: 2rem;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.back-button {
  background: none;
  border: none;
  color: #667eea;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(102, 126, 234, 0.1);
}

.step-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.activation-form {
  width: 100%;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.optional {
  font-size: 0.85rem;
  font-weight: 400;
  color: #718096;
}

.form-input {
  padding: 1rem 1.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  background: white;
  transition: all 0.3s ease;
  outline: none;
}

.form-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.code-input {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  letter-spacing: 2px;
  text-align: center;
  font-size: 1.1rem;
  text-transform: uppercase;
}

.input-hint {
  font-size: 0.85rem;
  color: #718096;
  text-align: center;
  line-height: 1.4;
}

.device-info-card {
  background: linear-gradient(135deg, #f7fafc, #edf2f7);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 1.5rem;
}

.device-info-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.device-icon {
  font-size: 1.25rem;
}

.device-title {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
}

.device-id-display {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.device-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #4a5568;
}

.device-value {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.85rem;
  color: #2d3748;
  background: white;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.device-note {
  font-size: 0.8rem;
  color: #718096;
  font-style: italic;
  text-align: center;
}

.error-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 12px;
  padding: 1rem;
}

.error-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.error-content {
  flex: 1;
}

.error-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #c53030;
  margin-bottom: 0.25rem;
}

.error-message {
  font-size: 0.85rem;
  color: #9b2c2c;
  line-height: 1.4;
}

/* Success Step */
.success-step {
  padding: 3rem 2rem;
  text-align: center;
}

.success-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  animation: bounce 0.6s ease-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.success-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 1rem 0;
}

.success-message {
  font-size: 1.1rem;
  color: #4a5568;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.success-details {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 2rem 0;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #4a5568;
}

.detail-value {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  color: #2d3748;
}

.loading-progress {
  margin-top: 2rem;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
  animation: progress 2s ease-out;
}

@keyframes progress {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

.progress-text {
  font-size: 0.9rem;
  color: #718096;
  margin: 0;
}

/* Buttons */
.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  outline: none;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

.btn-submit {
  width: 100%;
  min-height: 56px;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.btn-primary:hover .btn-arrow {
  transform: translateX(3px);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Footer */
.activation-footer {
  margin-top: 2rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
}

.support-section {
  margin-bottom: 1rem;
}

.support-text {
  font-size: 0.9rem;
  margin: 0;
}

.support-link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.support-link:hover {
  color: white;
}

.branding {
  font-size: 0.8rem;
  opacity: 0.7;
}

.branding p {
  margin: 0;
}

/* RTL Support */
[dir="rtl"] .feature-item:hover {
  transform: translateX(-5px);
}

[dir="rtl"] .step-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .device-info-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .error-card {
  flex-direction: row-reverse;
}

/* Responsive Design */
@media (max-width: 768px) {
  .activation-page {
    padding: 1rem;
  }
  
  .welcome-step,
  .success-step {
    padding: 2rem 1.5rem;
  }
  
  .code-entry-step {
    padding: 1.5rem;
  }
  
  .welcome-title {
    font-size: 2rem;
  }
  
  .step-title {
    font-size: 1.5rem;
  }
  
  .feature-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .device-id-display {
    flex-direction: column;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .activation-card {
    border-radius: 16px;
  }
  
  .welcome-title {
    font-size: 1.8rem;
  }
  
  .welcome-subtitle {
    font-size: 1rem;
  }
  
  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .back-button {
    align-self: flex-start;
  }
}
